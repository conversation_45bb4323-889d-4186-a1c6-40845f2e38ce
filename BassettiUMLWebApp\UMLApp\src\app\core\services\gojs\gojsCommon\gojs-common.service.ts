import { Injectable } from '@angular/core';
import go from 'gojs';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
  NodeUpdateCondition,
  UpdateCondition,
  UpdateProperties,
} from 'src/app/shared/model/gojs';
import { TreeNode } from 'src/app/shared/model/treeNode';
import { CardinalityService } from '../../cardinality/cardinality.service';

@Injectable({
  providedIn: 'root',
})
export class GojsCommonService {
  private _gojsDiagramSubject = new BehaviorSubject<go.Diagram | null>(null);
  constructor(private cardinalityService: CardinalityService) {}

  /**
   * Sets the GoJS diagram instance.
   *
   * This method emits the specified `go.Diagram` instance to the `_gojsDiagramSubject`,
   * allowing it to be shared or accessed throughout the application.
   *
   * @param diagram - The `go.Diagram` instance to be set as the current diagram.
   */
  setGojsDiagram(diagram: go.Diagram | null): void {
    this._gojsDiagramSubject.next(diagram);
  }

  /**
   * Subscribes to changes in the GoJS diagram instance.
   *
   * This method provides an observable stream of `go.Diagram` values, allowing
   * consumers to react to updates in the current GoJS diagram instance.
   *
   * @returns An `Observable` that emits the current `go.Diagram` instance, or `null` if none is set.
   */
  gojsDiagramChanges(): Observable<go.Diagram | null> {
    return this._gojsDiagramSubject.asObservable();
  }

  /**
   * Updates the opacity of an RGBA color.
   *
   * @private
   * @param rgbaColor The original RGBA color string.
   * @param newOpacity The desired opacity level as a number between 0 and 1.
   * @returns {string} A new RGBA color string with the updated opacity.
   *
   * @memberOf DiagramEditorComponent
   */
  updateRGBAColorWithOpacity(rgbaColor: string, newOpacity: number): string {
    // Extract RGBA components
    const rgbaValues = rgbaColor.match(/\d+(\.\d+)?/g);
    if (rgbaValues) {
      const red = parseFloat(rgbaValues[0]);
      const green = parseFloat(rgbaValues[1]);
      const blue = parseFloat(rgbaValues[2]);

      // Adjust the alpha value (opacity)
      const adjustedAlpha = newOpacity;

      // Update the RGBA color with the new opacity
      const newRgbaColor = `rgba(${red}, ${green}, ${blue}, ${adjustedAlpha})`;

      return newRgbaColor;
    } else {
      // Handle the case where rgbaValues is null
      return rgbaColor; // Return the original color as fallback
    }
  }

  isGojsDiagramClassNode(node: any): node is GojsDiagramClassNode {
    return (
      (node && node.category === GojsNodeCategory.Class) ||
      (node.category === GojsNodeCategory.AssociativeClass &&
        typeof node.idTemplateClass === 'number')
    );
  }
  isGojsDiagramEnumerationNode(node: any): node is GojsDiagramEnumerationNode {
    return (
      node &&
      node.category === GojsNodeCategory.Enumeration &&
      typeof node.idTemplateEnumeration === 'number'
    );
  }

  isGojsDiagramAttributeNode(node: any): node is GojsDiagramAttributeNode {
    return (
      node &&
      (node.category === GojsNodeCategory.Attribute ||
        node.category === GojsNodeCategory.Operation)
    );
  }
  isGojsDiagramLiteralNode(node: any): node is GojsDiagramLiteralNode {
    return node && node.category === GojsNodeCategory.EnumerationLiteral;
  }
  isGojsPaletteFolderNode(node: any): node is GojsFolderNode {
    return node && node.category === GojsNodeCategory.Folder;
  }

  isGojsLinkNode(node: any): node is GojsLinkNode {
    return node && node.category === GojsNodeCategory.Association;
  }

  /**
   * Updates properties of items within node data objects in the GoJS model's nodeDataArray based on a condition.
   *
   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).
   * @param {UpdateCondition} condition - A function to check if an item meets the update criteria.
   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.
   */
  updateNodeDataItemsProperties(
    model: go.Model,
    condition: UpdateCondition,
    properties: UpdateProperties
  ): void {
    model.nodeDataArray.forEach((nodeData) => {
      if (nodeData && nodeData['items']) {
        nodeData['items'].forEach((item: any) => {
          if (condition(item)) {
            for (const key in properties) {
              if (properties.hasOwnProperty(key)) {
                model.commit((model) => {
                  model.set(item, key, properties[key]);
                }, null);
              }
            }
          }
        });
      }
    });
  }

  /**
   * Updates properties of node data objects in the GoJS model's nodeDataArray based on a condition.
   *
   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).
   * @param {NodeUpdateCondition} condition - A function to check if a node data object meets the update criteria.
   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.
   */
  updateNodeDataProperties(
    model: go.Model,
    condition: NodeUpdateCondition,
    properties: UpdateProperties
  ): void {
    model.nodeDataArray.forEach((nodeData) => {
      if (condition(nodeData)) {
        for (const key in properties) {
          if (properties.hasOwnProperty(key)) {
            model.commit((model) => {
              model.set(nodeData, key, properties[key]);
            }, null);
          }
        }
      }
    });
  }

  /**
   * Sets multiple properties on a GoJS model data object.
   *
   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`, `go.GraphObject`).
   * @param {go.ObjectData} data - The data object (node or link data) to update.
   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.
   */
  setDataProperties(
    model: go.Model,
    data: go.ObjectData,
    properties: UpdateProperties
  ): void {
    Object.keys(properties).forEach((key) => {
      model.commit((m) => {
        m.set(data, key, properties[key]);
      }, null);
    });
  }

  /**
   *Commit transaction For updating the diagram node data

   * @param {*} propertyData
   * @memberof DiagramEditorComponent
   */
  commitGroupNodeData(
    nodeData: go.ObjectData[],
    properties: UpdateProperties,
    gojsDiagram: go.Diagram
  ): void {
    nodeData.forEach((node) => {
      this.setDataProperties(gojsDiagram.model, node, properties);
    });
  }

  /**
   * For removing the group node like class and enumeration node with children from palette and diagram
   * @param {GojsNodeCategory} category - The Category of node
   * @param {go.Diagram} goJsDiagram - Diagram
   * @memberof GojsCommonService
   */
  removeGroupNodeWithItems(
    idTemplate: number,
    category: GojsNodeCategory,
    goJsDiagram: go.Diagram
  ) {
    const diagramNodes = goJsDiagram.model.nodeDataArray.filter(
      (node) =>
        (node['idTemplateClass'] === idTemplate &&
          node['category'] === category) ||
        (node['idTemplateEnumeration'] === idTemplate &&
          node['category'] === category)
    );
    goJsDiagram.model.removeNodeDataCollection(diagramNodes);
  }

  removeNodeFromDiagram(
    goJsDiagram: go.Diagram,
    condition: NodeUpdateCondition
  ): void {
    const model = goJsDiagram.model as go.GraphLinksModel;
    model.nodeDataArray.forEach((nodeData) => {
      if (condition(nodeData)) {
        model.removeNodeData(nodeData);
      }
    });
  }

  removeAssociationLink(diagram: go.Diagram, classData: GojsDiagramClassNode) {
    const relatedLinkData = (
      diagram.model as go.GraphLinksModel
    ).linkDataArray.filter(
      (link) =>
        link['idSourceTempClass'] == classData.idTemplateClass ||
        link['idDestinationTempClass'] == classData.idTemplateClass
    );

    if (relatedLinkData) {
      relatedLinkData.forEach((associationLink) => {
        this.removeLinkToLink(
          diagram,
          (link) =>
            link.to === associationLink['labelKeys'][0] &&
            link.category === GojsNodeCategory.LinkToLink
        );
      });
      (diagram.model as go.GraphLinksModel).removeLinkDataCollection(
        relatedLinkData
      );
    }
  }

  removeLinkToLink(
    diagram: go.Diagram,
    condition: (link: any) => boolean,
    idAssociativeClass?: number
  ): void {
    const model = diagram.model as go.GraphLinksModel;
    const linksToRemove = model.linkDataArray.find(condition);
    if (linksToRemove) {
      model.removeLinkData(linksToRemove);
      this.cardinalityService.removeLinkToLinkByProperty(
        'id',
        linksToRemove['key']
      );
    } else if (idAssociativeClass) {
      this.cardinalityService.removeLinkToLinkByProperty(
        'idAssociativeClass',
        idAssociativeClass
      );
    }
  }

  checkGroupNodeExist(gojsDiagram: go.Diagram, nodeData: TreeNode): boolean {
    const existingNode = gojsDiagram.model.nodeDataArray.find((node) => {
      return node['treeNodeTag'] === nodeData.tag;
    });
    if (existingNode) {
      // Select the node using the diagram's selection manager
      const part = gojsDiagram.findPartForData(existingNode);
      if (part) {
        // gojsDiagram.clearSelection();
        gojsDiagram.select(part); // select the part
      }

      return true;
    }

    // else if (nodeData.category === GojsNodeCategory.AssociativeClass) {
    //   // If one link to link is already present, select it and restrict to drop another associative class which have link to link with the same link
    //   const linkToLink = this.cardinalityService
    //     .getLinkToLinks()
    //     .find(
    //       (link) =>
    //         link.idAssociativeClass ==
    //         (nodeData.data as GojsDiagramClassNode).idTemplateClass
    //     );
    //   if (linkToLink) {
    //     const existingLinkToLink = (
    //       gojsDiagram.model as go.GraphLinksModel
    //     ).linkDataArray.find((node) => {
    //       return (
    //         node['category'] === GojsNodeCategory.LinkToLink &&
    //         node['idLink'] === linkToLink.idLink
    //       );
    //     });
    //     if (existingLinkToLink) {
    //       // Select the node using the diagram's selection manager
    //       const part = gojsDiagram.findPartForData(existingLinkToLink);
    //       if (part) {
    //         gojsDiagram.clearSelection();
    //         gojsDiagram.select(part); // select the part
    //       }
    //       return true;
    //     }
    //   }
    // }
    return false;
  }

  selectMultipleGroupNodeExist(goJsDiagram: go.Diagram, nodeData: TreeNode[]) {
    const existingParts: go.Part[] = [];

    nodeData.forEach((data) => {
      const matchingNode = goJsDiagram.model.nodeDataArray.find(
        (node) => node['treeNodeTag'] === data.tag
      );
      if (matchingNode) {
        const part = goJsDiagram.findPartForData(matchingNode);
        if (part) {
          existingParts.push(part);
        }
      }
    });

    if (existingParts.length > 0) {
      goJsDiagram.clearSelection(); // Clear any previous selection
      goJsDiagram.selectCollection(existingParts); // Select all matched parts
      goJsDiagram.commandHandler.scrollToPart(existingParts[0]); // Optional: bring first selected node into view
    }
  }
}
