import { Injectable } from '@angular/core';
import go from 'gojs';
import {
  CardinalityCreate,
  DeletedLink,
  LinkToLink,
} from 'src/app/shared/model/cardinality';
import { CommentPatch } from 'src/app/shared/model/comment';
import { Diagram } from 'src/app/shared/model/diagram';
import { Enumeration } from 'src/app/shared/model/enumeration';
import {
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsLinkNode,
  GoJsLinkToLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType } from 'src/app/shared/model/project';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { GojsDiagramAttributeNode } from '../../../shared/model/gojs';
import { AccessService } from '../access/access.service';
import { ClassService } from '../class/class.service';
import { CommentService } from '../comment/comment.service';
import { EnumerationService } from '../enumeration/enumeration.service';
import { GojsCommentService } from '../gojs/gojs-comment/gojs-comment.service';
import { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';
import { GojsCardinalityService } from '../gojs/gojsCardinality/gojs-cardinality.service';
import { GojsClassService } from '../gojs/gojsClass/gojs-class.service';
import { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';
import { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';
import { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';
import { PropertyService } from '../property/property.service';

@Injectable({
  providedIn: 'root',
})
export class EventListenerService {
  private hasEditAccessOnly!: boolean;
  private diagram!: go.Diagram;
  private currentDiagram!: Diagram;
  constructor(
    private accessService: AccessService,
    private classService: ClassService,
    private goJsCardinalityService: GojsCardinalityService,
    private propertyService: PropertyService,
    private diagramUtils: DiagramUtils,
    private commonGojsService: GojsCommonService,
    private goJsEnumerationService: GojsEnumerationService,
    private enumerationService: EnumerationService,
    private gojsCommentService: GojsCommentService,
    private goJsAttrService: GojsAttributeService,
    private gojsClassService: GojsClassService,
    private goJsLiteralService: GojsLiteralService,
    private commentService: CommentService
  ) {
    this.accessService.accessTypeChanges().subscribe((response) => {
      if (response != AccessType.Viewer) {
        this.hasEditAccessOnly = true;
      } else {
        this.hasEditAccessOnly = false;
      }
    });
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });

    this.commonGojsService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this.diagram = diagram;
    });
  }

  /**
   * Adds event listeners to the diagram for handling model changes and other interactions.
   * @memberof DiagramEditorComponent
   */
  addDiagramEventListeners(): void {
    this.addSelectionDeletingListener();
    this.addPartResizedListener();
    this.addTextEditedListener();
    this.addLinkDrawnListener();
    this.addLinkReDrawnListener();
    this.addModelChangedListener();
  }

  /**
   * Adds a listener for the 'SelectionDeleting' event on the diagram.
   * @private
   */
  private addSelectionDeletingListener(): void {
    this.diagram.addDiagramListener(
      'SelectionDeleting',
      this.addDeletingEventListener
    );
  }

  /**
   * Selection delete event listener for deleting the selective class
   * @private
   * @param {go.DiagramEvent} event
   * @memberof DiagramEditorComponent
   */
  addDeletingEventListener = (event: go.DiagramEvent) => {
    if (this.hasEditAccessOnly) {
      const nodes = event.subject;
      const classIds: number[] = [],
        enumIds: number[] = [];
      event.cancel=true;
      nodes.each((node: go.Node) => {
        if (
          node.data.category == GojsNodeCategory.Class ||
          node.data.category == GojsNodeCategory.AssociativeClass
        ) {
          classIds.push(node.data.id);
        } else if (node.data.category == GojsNodeCategory.Enumeration) {
          enumIds.push(node.data.id);
        }
        if (node.data.category == GojsNodeCategory.Association) {
          // delete links here
          this.goJsCardinalityService.deleteLink(
            node,
            nodes.count,
            this.diagram,
            event
          );
        }
        if (node.data.category == GojsNodeCategory.Comment) {
          this.gojsCommentService.handleCommentDelete([node.data.id]);
        }
      });
      if (classIds.length > 0) this.classService.deleteClasses(classIds);
      if (enumIds.length > 0)
        this.enumerationService.deleteEnumerations(enumIds);
      this.propertyService.setPropertyData(null);
    }
  };

  /**
   * Adds a listener for the 'PartResized' event on the diagram.
   * @private
   */
  private addPartResizedListener(): void {
    this.diagram.addDiagramListener('PartResized', (e) => {
      if (this.hasEditAccessOnly) {
        this.handlePartResized(e);
      }
    });
  }

  /**
   * Handles the 'PartResized' event on the diagram.
   * @private
   * @param {go.DiagramEvent} event - The event object.
   */
  private handlePartResized(event: go.DiagramEvent): void {
    const shape = event.subject as go.Shape;
    let diagramData = event.diagram.selection.first()?.data;
    diagramData.size = new go.Size(
      shape.actualBounds.width,
      shape.actualBounds.height
    );
    if (
      diagramData.category === GojsNodeCategory.Class ||
      diagramData.category === GojsNodeCategory.AssociativeClass
    ) {
      this.updateClass(diagramData);
    } else if (diagramData.category === GojsNodeCategory.Enumeration) {
      this.goJsEnumerationService.updateExistingEnum(
        diagramData as GojsDiagramEnumerationNode,
        false
      );
    } else if (diagramData.category === GojsNodeCategory.Comment) {
      this.gojsCommentService.updateComment(
        diagramData.name,
        diagramData.description,
        diagramData,
        event.diagram
      );
    }
  }

  /**
   * Updates the class based on the resized data.
   * @private
   * @param {GojsDiagramClassNode} diagramData - The data of the resized diagram element.
   */
  private updateClass(diagramData: GojsDiagramClassNode): void {
    this.classService.updateClass({
      id: diagramData.id,
      name: diagramData.name,
      idDiagram: this.currentDiagram.id!,
      key: diagramData.key,
      isAssociative: diagramData.isAssociative,
      property: {
        position: diagramData.position!,
        height: diagramData.size.height,
        width: diagramData.size.width,
        icon: diagramData.icon,
        color: diagramData.color,
      },
    });
  }

  /**
   * Adds a listener for the 'TextEdited' event on the diagram.
   * @private
   */
  private addTextEditedListener(): void {
    this.diagram.addDiagramListener('TextEdited', (e) => {
      if (
        e.subject.panel &&
        (e.subject.panel.data ||
          e.diagram.selection.first()?.data.category ===
            GojsNodeCategory.Association) &&
        this.hasEditAccessOnly
      ) {
        this.handleTextEditedListener(e);
      }
    });
  }

  /**
   * Handles the editing of text elements on the diagram.
   * Updates links or attributes based on the edited data.
   * @private
   * @param {go.DiagramEvent} event - The event object.
   */
  private handleTextEditedListener(event: go.DiagramEvent): void {
    if (
      event.diagram.selection &&
      event.diagram.selection.first()?.data &&
      event.diagram.selection.first()?.data.category ==
        GojsNodeCategory.Association
    ) {
      this.goJsCardinalityService.updateLinkOnTextEdited(
        event.diagram.selection.first()?.data
      );
    } else if (
      event.subject.panel.data.category == GojsNodeCategory.Attribute ||
      event.subject.panel.data.category == GojsNodeCategory.Operation
    ) {
      this.goJsAttrService.updateAttributeOnTextEdited(event, this.diagram);
    } else if (
      event.subject.panel.data.category == GojsNodeCategory.EnumerationLiteral
    ) {
      this.goJsLiteralService.handleEditLiteralNameInDiagram(
        event,
        this.diagram
      );
    }
  }

  /**
   * Adds a listener for the 'LinkDrawn' event on the diagram.
   * @private
   */
  private addLinkDrawnListener(): void {
    this.diagram.addDiagramListener('LinkDrawn', (event) => {
      if (this.hasEditAccessOnly) {
        this.handleLinkDrawn(event);
      }
    });
  }

  /**
   * Handles the event when a link is drawn on the diagram.
   * Checks if the link is between valid nodes and creates or updates the link accordingly.
   * @private
   * @param {go.DiagramEvent} event - The event object.
   */
  private handleLinkDrawn(event: go.DiagramEvent): void {
    const linkData: GojsLinkNode | GoJsLinkToLinkNode = event.subject.data;
    const toNode = this.diagramUtils.getObjectDataByKey(
      this.diagram,
      linkData.to
    );
    const fromNode = this.diagramUtils.getObjectDataByKey(
      this.diagram,
      linkData.from
    );
    if (toNode && fromNode) {
      if (
        fromNode['category'] == GojsNodeCategory.AssociativeClass &&
        toNode['category'] == GojsNodeCategory.LinkLabel
      ) {
        const linkToLinkObj = this.createLinkToLinkObject(
          fromNode,
          toNode,
          linkData as GoJsLinkToLinkNode
        );
        this.goJsCardinalityService.createNewLinkToLinkAndUpdate(
          linkData as GoJsLinkToLinkNode,
          linkToLinkObj,
          this.diagram
        );
      } else {
        const deletedLink = this.findDeletedLink(fromNode, toNode);
        const newLink = this.createLinkObject(
          fromNode,
          toNode,
          linkData as GojsLinkNode
        );
        if (deletedLink) {
          this.goJsCardinalityService.handleDeletedLink(
            deletedLink,
            linkData as GojsLinkNode,
            newLink,
            fromNode,
            toNode,
            this.diagram
          );
        } else {
          this.goJsCardinalityService.createNewLinkAndUpdate(
            linkData as GojsLinkNode,
            newLink,
            fromNode,
            toNode,
            this.diagram
          );
          event.diagram.clearSelection();
        }
      }
    }
  }

  /**
   * Finds a deleted link between two nodes.
   * @private
   * @param {go.ObjectData} fromNode - The source node.
   * @param {go.ObjectData} toNode - The destination node.
   * @returns {DeletedLink | undefined } The deleted link if found, otherwise null.
   */
  private findDeletedLink(
    fromNode: go.ObjectData,
    toNode: go.ObjectData
  ): DeletedLink | undefined {
    return this.diagramUtils
      .getDeletedLinks()
      .find(
        (link) =>
          (link.idSourceClass == fromNode['id'] &&
            link.idDestinationClass == toNode['id']) ||
          (link.idSourceClass == toNode['id'] &&
            link.idDestinationClass == fromNode['id'])
      );
  }

  /**
   * Creates a new link object based on the source and destination nodes.
   * @private
   * @param {go.ObjectData} fromNode - The source node.
   * @param {go.ObjectData} toNode - The destination node.
   * @param {GojsLinkNode} linkData - The link data.
   * @returns {CardinalityDetails} The new link object.
   */
  private createLinkObject(
    fromNode: go.ObjectData,
    toNode: go.ObjectData,
    linkData: GojsLinkNode
  ): CardinalityCreate {
    return {
      name: `${fromNode['name']} - ${toNode['name']}`,
      id: linkData.id,
      idSourceTempClass: fromNode['idTemplateClass'],
      idDestinationTempClass: toNode['idTemplateClass'],
      idLinkType: 1,
      sourcePort: linkData.fromPort!,
      destinationPort: linkData.toPort!,
      idDiagram: this.currentDiagram.id!,
      color: 'rgba(0, 0, 0, 1)',
      fromComment: linkData.fromComment,
      toComment: linkData.toComment,
      segmentOffset: '0 0',
      linkPort: {
        idDiagram: this.currentDiagram.id!,
        destinationPort: linkData.toPort!,
        sourcePort: linkData.fromPort!,
        idLink: linkData.id!,
        segmentOffset: '0 0',
      },
    };
  }
  private createLinkToLinkObject(
    fromNode: go.ObjectData,
    toNode: go.ObjectData,
    linkData: GoJsLinkToLinkNode
  ): LinkToLink {
    return {
      idLink: toNode['idLink'],
      idAssociativeClass: fromNode['idTemplateClass'],
      port: linkData.fromPort!,
    };
  }

  private addLinkReDrawnListener() {
    this.diagram.addDiagramListener('LinkRelinked', (event) => {
      if (this.hasEditAccessOnly) {
        const linkData = event.subject.data;
        if (linkData) {
          if (linkData.category == GojsNodeCategory.LinkToLink) {
            this.goJsCardinalityService.updatePortForLinkToLink(linkData);
          } else {
            this.goJsCardinalityService.handleReDrawnLink(
              linkData,
              this.diagram
            );
            event.diagram.clearSelection();
          }
        }
      }
    });
  }

  /**
   * Adds a listener for the 'ModelChanged' event on the diagram.
   * @private
   */
  private addModelChangedListener(): void {
    this.diagram.addModelChangedListener((event) => {
      if (event.isTransactionFinished) {
        const action =
          event.propertyName === 'FinishedUndo'
            ? 'undo'
            : event.propertyName === 'FinishedRedo'
            ? 'redo'
            : null;
        if (action) {
          this.handleUndoRedo(event, action);
        }

        const isLabelShifted =
          event.propertyName === 'CommittedTransaction' &&
          event.oldValue === 'Shifted Label';
        if (isLabelShifted) {
          if (event.object) {
            let segmentOffset;
            let linkData;
            event.object['changes']['iterator'].each(
              (change: go.ObjectData) => {
                if (change['propertyName'] === 'segmentOffset') {
                  segmentOffset = change['newValue'];
                  linkData = change['object'];
                }
              }
            );
            this.goJsCardinalityService.updateLinkOnTextEdited(
              linkData! as GojsLinkNode
            );
          }
        }
      }
    });
  }

  /**
   * Handles undo and redo operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  private handleUndoRedo(event: go.ChangedEvent, action: string): void {
    if (event.object) {
      if (event.object['name'] == 'Delete') {
        this.handleDelete(event, action);
      } else if (event.object['name'] == 'ExternalCopy') {
        this.handleExternalCopy(event, action);
      } else if (event.object['name'] == 'Linking') {
        this.goJsCardinalityService.handleUndoRedoLinking(event, action, false);
      } else if (event.object['name'] == 'Move') {
        this.handleMove(event, action);
      } else if (event.object['name'] == 'Resizing') {
        this.handleResizing(event, action);
      } else if (event.object['name'] == 'TextEditing') {
        this.handleTextEditingUndoRedo(event, action);
      } else if (event.object['name'] == 'Layout') {
        this.handleLayoutChanges(event, action);
      } else if (event.object['name'] == '') {
        this.handleDelete(event, action);
      }
    }
  }

  private handleTextEditingUndoRedo(event: go.ChangedEvent, action: string) {
    if (!event.object) return;

    const changesIterator = event.object['changes']['iterator'];

    changesIterator.each((change: go.ObjectData) => {
      if (change['propertyName'] === 'text') {
        this.handleTextChange(change, action);
      } else if (change['propertyName'] === 'description') {
        this.handleDescriptionChange(change, action);
      }
    });
  }

  private handleTextChange(change: go.ObjectData, action: string) {
    const modifiedName =
      action === 'undo' ? change['oldValue'] : change['newValue'];

    const nodeValue = this.getSelectedNode(change);
    if (!nodeValue) return;

    switch (nodeValue.data.category) {
      case GojsNodeCategory.Comment:
        this.gojsCommentService.updateComment(
          modifiedName,
          nodeValue.data.description,
          nodeValue.data,
          this.diagram
        );
        break;
      case GojsNodeCategory.Association:
        this.goJsCardinalityService.updateLinkOnTextEdited({
          ...nodeValue.data,
        });
        break;
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.gojsClassService.updateTemplateClass(nodeValue.data, this.diagram);
        break;
      case GojsNodeCategory.Enumeration:
        this.goJsEnumerationService.updateEnumerationFromDiagram(
          nodeValue.data,
          this.diagram
        );
        break;
    }
  }

  private handleDescriptionChange(change: go.ObjectData, action: string) {
    const modifiedDescription =
      action === 'undo' ? change['oldValue'] : change['newValue'];

    const nodeValue = this.getSelectedNode(change);
    if (nodeValue?.data.category === GojsNodeCategory.Comment) {
      this.gojsCommentService.updateComment(
        nodeValue.data.name,
        modifiedDescription,
        nodeValue.data,
        this.diagram
      );
    }
  }

  private getSelectedNode(change: go.ObjectData): go.Part | null {
    // First try to get the node from the change's diagram selection
    const diagramFromChange = change['diagram'];
    if (diagramFromChange?.selection?.first) {
      return diagramFromChange.selection.first() || null;
    }

    // If that fails, try to get it from the current diagram's selection
    if (this.diagram?.selection?.first) {
      return this.diagram.selection.first() || null;
    }

    // If we have an object in the change, try to find the corresponding node
    if (change['object'] && change['object'].key && this.diagram) {
      const key = change['object'].key;
      return this.diagram.findNodeForKey(key) || null;
    }

    // As a last resort, if we have data in the change, try to find the node by data
    if (change['object'] && this.diagram) {
      return null; // Return null for now, but we could implement additional lookup logic if needed
    }

    return null;
  }

  /**
   * Handles delete operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  private handleDelete(event: go.ChangedEvent, action: string): void {
    if (!event.object) return;
    const changesIterator = event.object['changes']['iterator'];
    changesIterator.each((change: go.ObjectData) => {
      if (change['propertyName'] === 'nodeDataArray') {
        this.handleNodeDeletion(change['oldValue'], action);
      } else if (change['propertyName'] === 'linkDataArray') {
        this.handleLinkDeletion(event, action);
      }
    });
  }

  private handleNodeDeletion(nodeData: any, action: string): void {
    if (!nodeData) return;
    const nodeId = nodeData.id;
    const category = nodeData.category;
    if (action === 'undo') {
      this.undoNodeDeletion(category, nodeId);
    } else {
      this.performNodeDeletion(category, nodeId);
    }
  }

  private undoNodeDeletion(category: GojsNodeCategory, nodeId: number): void {
    switch (category) {
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.classService.undoClassDeletion(nodeId);
        break;
      case GojsNodeCategory.Enumeration:
        this.enumerationService.undoEnumDeletion(nodeId);
        break;
      case GojsNodeCategory.Comment:
        this.commentService.undoCommentDeletion(nodeId);
        break;
    }
  }

  private performNodeDeletion(
    category: GojsNodeCategory,
    nodeId: number
  ): void {
    switch (category) {
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.classService.deleteClasses([nodeId]);
        break;
      case GojsNodeCategory.Enumeration:
        this.enumerationService.deleteEnumerations([nodeId]);
        break;
      case GojsNodeCategory.Comment:
        this.commentService.deleteComment([nodeId]);
        break;
    }
  }

  private handleLinkDeletion(event: go.ChangedEvent, action: string): void {
    this.goJsCardinalityService.handleUndoRedoLinking(
      event,
      action === 'undo' ? 'redo' : 'undo',
      true
    );
  }

  /**
   * Handles external copy operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  private handleExternalCopy(event: go.ChangedEvent, action: string): void {
    if (!event.object) return;
    const changesIterator = event.object['changes']['iterator'];
    changesIterator.each((change: go.ObjectData) => {
      if (change['propertyName'] === 'nodeDataArray') {
        const nodeData = change['newValue'];
        if (!nodeData) return;

        if (action === 'redo') {
          this.restoreNodeData(nodeData);
        } else {
          this.deleteNodeData(nodeData);
          this.diagram.model.updateTargetBindings(nodeData);
        }
      }
    });
  }

  private restoreNodeData(nodeData: any): void {
    switch (nodeData.category) {
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.classService.undoClassDeletion(nodeData.id);
        break;
      case GojsNodeCategory.Enumeration:
        this.enumerationService.undoEnumDeletion(nodeData.id);
        break;
      case GojsNodeCategory.Comment:
        this.commentService.undoCommentDeletion(nodeData.id);
        break;
    }
  }

  private deleteNodeData(nodeData: any): void {
    switch (nodeData.category) {
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.classService.deleteClasses([nodeData.id]);
        break;
      case GojsNodeCategory.Enumeration:
        this.enumerationService.deleteTemplateEnums([nodeData.id]);
        break;
      case GojsNodeCategory.Comment:
        this.commentService.deleteComment([nodeData.id]);
        break;
    }
  }

  /**
   * Handles move operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  private handleMove(event: go.ChangedEvent, action: string): void {
    if (event.object) {
      const nodeData = event.object['changes']['iterator']
        .filter(
          (obj: go.ObjectData) =>
            obj['propertyName'] == 'position' && !obj['object'].data
        )
        .first();
      if (nodeData) {
        if (
          nodeData.object.category == GojsNodeCategory.Class ||
          nodeData.object.category == GojsNodeCategory.AssociativeClass
        ) {
          this.classService.updateClass({
            id: nodeData.object.id,
            idDiagram: this.currentDiagram.id!,
            idTemplateClass: nodeData.object.idTemplateClass,
            key: nodeData.object.key,
            name: nodeData.object.name,
            description: nodeData.object.description,
            volumetry: nodeData.object.volumetry,
            tag: nodeData.object.tag,
            isAssociative:
              nodeData.object.category == GojsNodeCategory.AssociativeClass
                ? true
                : false,
            property: {
              color: nodeData.object.color,
              height: nodeData.object.size.height,
              width: nodeData.object.size.width,
              icon: nodeData.object.icon,
              position:
                action === 'undo' ? nodeData.oldValue : nodeData.newValue,
            },
          });
        } else if (nodeData.object.category == GojsNodeCategory.Enumeration) {
          this.enumerationService.updateEnumeration({
            id: nodeData.object.id,
            idDiagram: this.currentDiagram.id!,
            key: nodeData.object.key,
            name: nodeData.object.name,
            description: nodeData.object.description,
            volumetry: nodeData.object.volumetry,
            tag: nodeData.object.tag,
            idTemplateEnumeration: nodeData.object.idTemplateEnumeration,
            property: {
              color: nodeData.object.color,
              height: nodeData.object.size.height,
              width: nodeData.object.size.width,
              icon: nodeData.object.icon,
              position:
                action === 'undo' ? nodeData.oldValue : nodeData.newValue,
            },
          } as Enumeration);
        } else if (nodeData.object.category == GojsNodeCategory.Comment) {
          this.commentService.updateExistingComment({
            id: nodeData.object.id,
            name: nodeData.object.name,
            description: nodeData.object.description,
            height: nodeData.object.size.height,
            width: nodeData.object.size.width,
            position: action === 'undo' ? nodeData.oldValue : nodeData.newValue,
          } as CommentPatch);
        }
      }
    }
  }

  /**
   * Handles resizing operations for the diagram.
   * @private
   * @param {go.ChangedEvent} event - The change event.
   * @param {string} action - The action type ('undo' or 'redo').
   * @memberof DiagramEditorComponent
   */
  private handleResizing(event: go.ChangedEvent, action: string): void {
    if (event.object) {
      const nodeData = event.object['changes']['iterator'].filter(
        (obj: go.ObjectData) =>
          obj['propertyName'] == 'desiredSize' && !obj['object'].data
      )[0];
      if (nodeData) {
        this.classService.updateClass({
          id: nodeData.object.id,
          idDiagram: this.currentDiagram.id!,
          idTemplateClass: nodeData.object.idTemplateClass,
          key: nodeData.object.key,
          name: nodeData.object.name,
          isAssociative:
            nodeData.object.category == GojsNodeCategory.AssociativeClass
              ? true
              : false,
          property: {
            color: nodeData.object.color,
            height:
              action === 'undo'
                ? nodeData.oldValue.height
                : nodeData.newValue.height,
            width:
              action === 'undo'
                ? nodeData.oldValue.width
                : nodeData.newValue.width,
            icon: nodeData.object.icon,
            position: nodeData.object.position,
          },
        });
      }
    }
  }

  private handleLayoutChanges(event: go.ChangedEvent, action: string) {
    if (event.object) {
      let attributes: GojsDiagramAttributeNode[] = [];
      event.object['changes']['iterator'].each((obj: go.ObjectData) => {
        if (obj['propertyName'] == 'itemArray') {
          attributes.push(...obj['oldValue']);
        }
      });
      attributes = Array.from(
        new Set(attributes.map((item) => JSON.stringify(item)))
      ).map((item) => JSON.parse(item));

      this.diagram.model.nodeDataArray.forEach((node) => {});
    }
  }
}
