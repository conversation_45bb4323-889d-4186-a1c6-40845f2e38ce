{"ast": null, "code": "import _asyncToGenerator from \"D:/GitCodeBassetti/devint-BASSETTI-GROUP-APP/BassettiUMLWebApp/UMLApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { DefaultDestinationPort, DefaultSourcePort } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../cardinality/cardinality.service\";\nimport * as i2 from \"../../access/access.service\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/shared/utils/diagram-utils\";\nimport * as i5 from \"../../property/property.service\";\nimport * as i6 from \"../gojsCommon/gojs-common.service\";\nimport * as i7 from \"../../snackbar/snack-bar.service\";\nexport class GojsCardinalityService {\n  constructor(cardinalityService, accessService, dialog, diagramUtils, propertyService, gojsCommonService, snackbarService) {\n    this.cardinalityService = cardinalityService;\n    this.accessService = accessService;\n    this.dialog = dialog;\n    this.diagramUtils = diagramUtils;\n    this.propertyService = propertyService;\n    this.gojsCommonService = gojsCommonService;\n    this.snackbarService = snackbarService;\n    this.hasEditAccess = false;\n    this.accessService.accessTypeChanges().subscribe(response => {\n      this.hasEditAccess = response != AccessType.Viewer;\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n  }\n  /**\n   * Validates whether a link between two nodes in a GoJS diagram is allowed based on predefined link relations and existing links.\n   * @param fromNode - The starting node of the link.\n   * @param toNode - The target node of the link.\n   * @param linkRelations - An array defining allowed relations between node categories.\n   * @param linkData - The data object representing the link being validated.\n   * @param gojsDiagram - The GoJS diagram instance containing the nodes and links.\n   * @returns A boolean indicating whether the link is valid.\n   */\n  validateLink(fromNode, toNode, linkRelations, linkData, fromPort, gojsDiagram) {\n    if (!fromNode?.data || !toNode?.data) return false;\n    const linkDataArray = JSON.parse(gojsDiagram.model.toJson())['linkDataArray'];\n    // To Prevent link between classes from same template class\n    if (fromNode.data.idTemplateClass == toNode.data.idTemplateClass && fromNode.data.id != toNode.data.id) return false;\n    //Condition for changing the toPort of associative class and preventing to draw multiple link for same associative class\n    if (fromNode.data.category == GojsNodeCategory.AssociativeClass && linkDataArray.some(link => link['idAssociativeClass'] && link['idAssociativeClass'] === fromNode.data.idTemplateClass) && linkData == null) {\n      return false;\n    }\n    // Find the relation that matches the category of the fromNode\n    const relation = linkRelations?.find(link => link.from === fromNode.data.category);\n    // If no relation exists for the fromNode category, the link is invalid\n    if (!relation) return false;\n    // Check if the toNode category is allowed by the relation\n    if (!relation.to.includes(toNode.data.category)) return false;\n    // If the linkData is provided, check for specific cardinality rules\n    if (linkData && Object.keys(linkData).length > 0) {\n      if (linkData.category === GojsNodeCategory.LinkToLink) {\n        return this.validateFromNodeLinkToLink(linkData, fromNode, toNode, fromPort);\n      } else {\n        return true;\n        // const cardinalityLinks = this.cardinalityService.getLinks();\n        // const isCardinalityValid = cardinalityLinks.some(\n        //   (link) =>\n        //     link.idSourceTempClass === fromNode.data.idTemplateClass ||\n        //     link.idDestinationTempClass === toNode.data.idTemplateClass\n        // );\n        // const isLinkAlreadyExists = linkDataArray.some(\n        //   (link: GojsLinkNode) =>\n        //     link.idFromClass === fromNode.data.id &&\n        //     link.idToClass === toNode.data.id\n        // );\n        // return isCardinalityValid && isLinkAlreadyExists;\n      }\n    } else if (fromNode.data.category == GojsNodeCategory.AssociativeClass) {\n      return this.validateLinkToLink(toNode);\n    }\n    // If all checks pass, the link is valid\n    return true;\n  }\n  validateLinkToLink(toNode) {\n    const linkToLinks = this.cardinalityService.getLinkToLinks();\n    const isLinkAlreadyExists = linkToLinks.some(link => link.idLink === toNode.data.idLink);\n    if (isLinkAlreadyExists) {\n      this.snackbarService.info('snackBar.linkToLinkAlreadyExists');\n    }\n    return !isLinkAlreadyExists;\n  }\n  // For checking the link to link from node port change is valid or not\n  validateFromNodeLinkToLink(linkData, fromNode, toNode, fromPort) {\n    const linkToLinks = this.cardinalityService.getLinkToLinks();\n    const isLinkAlreadyExists = linkToLinks.some(link => link.idLink === toNode.data.idLink);\n    if (linkData.data.fromPort !== fromPort) {\n      return linkData.data['idAssociativeClass'] === fromNode.data.idTemplateClass;\n    }\n    return !isLinkAlreadyExists;\n  }\n  /**\n   * Validates the relationship between two nodes (fromNode and toNode) based on the link's relation data.\n   * @param {go.Node} fromNode - The source node of the link to be validated.\n   * @param {go.Node} toNode - The target node of the link to be validated.\n   * @param {go.Link} link - The link to be validated.\n   * @param {go.Diagram} gojsDiagram - The diagram to which the link and nodes belong.\n   * @return {*}  {boolean} - Returns `true` if the link is valid based on the relations, otherwise returns `false`.\n   * @memberof GojsCardinalityService\n   */\n  validateGroupLink(fromNode, toNode, link, fromPort, gojsDiagram) {\n    if (link && link.data && link.data.linkRelations) {\n      return this.validateLink(fromNode, toNode, link.data.linkRelations, link, fromPort, gojsDiagram);\n    } else {\n      const linkRelations = [{\n        from: GojsNodeCategory.Class,\n        to: [GojsNodeCategory.Class]\n      }, {\n        from: GojsNodeCategory.AssociativeClass,\n        to: [GojsNodeCategory.LinkLabel]\n      }];\n      return this.validateLink(fromNode, toNode, linkRelations, link, fromPort, gojsDiagram);\n    }\n  }\n  /**\n   * Maps link data to the format required by the diagram.\n   * @param {CardinalityDetails} link The link data.\n   * @param {go.ObjectData} srcLink The source node data.\n   * @param {go.ObjectData} destLink The destination node data.\n   * @returns {GojsLinkNode} The mapped link data.\n   * @memberOf CardinalityService\n   */\n  mapLinkData(link, srcLink, destLink) {\n    let linkData = {\n      from: srcLink['key'],\n      to: destLink['key'],\n      name: link.name,\n      cardinalityFrom: this.cardinalityService.getLinkTypes().get(link.idLinkType)?.from,\n      cardinalityTo: this.cardinalityService.getLinkTypes().get(link.idLinkType)?.to,\n      key: link.id,\n      id: link.id,\n      idLinkType: link.idLinkType,\n      idSourceTempClass: link.idSourceTempClass,\n      idDestinationTempClass: link.idDestinationTempClass,\n      idFromClass: srcLink['id'],\n      idToClass: destLink['id'],\n      category: GojsNodeCategory.Association,\n      editable: this.hasEditAccess,\n      fromPort: link.linkPorts[0]?.sourcePort,\n      toPort: link.linkPorts[0]?.destinationPort,\n      color: link.color,\n      labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\n      fromComment: link.fromComment,\n      toComment: link.toComment,\n      segmentOffset: link.linkPorts[0]?.segmentOffset\n    };\n    this.cardinalityService.createLinkPort({\n      idLink: link.id,\n      idDiagram: this.currentDiagram.id,\n      sourcePort: link.linkPorts[0]?.sourcePort ?? 'R2',\n      destinationPort: link.linkPorts[0]?.destinationPort ?? 'L2',\n      segmentOffset: '0 0'\n    }).subscribe(createdLinkPort => {\n      this.cardinalityService.addNewLinkPort(link.id, createdLinkPort);\n    });\n    return linkData;\n  }\n  /**\n   * Deletes a link based on the node's data.\n   * @param {go.Node} node - The node containing the link to be deleted.\n   * @param {number} nodeCount - The number of links associated with the node.\n   * @param {go.Diagram} diagram - The diagram containing the node and link to be deleted.\n   * @memberof GojsCardinalityService\n   */\n  deleteLink(node, nodeCount, diagram, event) {\n    const linkData = node.data;\n    // Always show confirmation dialog for link deletion\n    const dialogRef = this.dialog.open(DialogConfirmationComponent, {\n      width: '320px',\n      data: {\n        title: 'dialog.deleteTitle',\n        reject: 'dialog.no',\n        confirm: 'dialog.yes'\n      }\n    });\n    dialogRef.afterClosed().subscribe(isConfirm => {\n      if (isConfirm) {\n        // User confirmed deletion - manually remove from diagram and handle business logic\n        this.performLinkDeletion(linkData, diagram, nodeCount);\n      }\n      // If user cancels (isConfirm is false), do nothing - link remains in diagram\n    });\n  }\n  /**\n   * Performs the actual link deletion after confirmation\n   * @private\n   * @param {GojsLinkNode} linkData - The link data to be deleted\n   * @param {go.Diagram} diagram - The diagram containing the link\n   * @param {number} nodeCount - The number of nodes selected\n   */\n  performLinkDeletion(linkData, diagram, nodeCount) {\n    // Manually remove the link from the diagram model\n    diagram.model.removeLinkData(linkData);\n    // Handle business logic based on selection count\n    if (nodeCount > 1) {\n      // Multiple nodes selected, remove link from current diagram only\n      this.removeLinkFromCurrentDiagram(linkData, diagram, true);\n    } else {\n      // Single node selected, remove link from all diagrams\n      this.removeLinkFromAllDiagram(linkData);\n    }\n  }\n  /**\n   * Handles linking operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleUndoRedoLinking(event, action, isDelete) {\n    if (event.object) {\n      event.object['changes']['iterator'].each(obj => {\n        if (obj['propertyName'] == 'linkDataArray') {\n          const valueParam = isDelete ? 'oldValue' : 'newValue';\n          if (action === 'undo') {\n            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\n              this.cardinalityService.removeLinkToLinkByProperty('id', obj[valueParam].key);\n              this.cardinalityService.deleteLinkToLink(obj[valueParam].key);\n            } else {\n              this.cardinalityService.removeLink(obj[valueParam].key);\n              this.cardinalityService.delete(obj[valueParam].key);\n            }\n          } else {\n            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\n              this.cardinalityService.addLinkToLink(obj[valueParam]);\n              this.cardinalityService.undoLinkToLinkDeletion(obj[valueParam].key);\n            } else {\n              this.cardinalityService.addLink(obj[valueParam]);\n              this.cardinalityService.undoLinkDeletion(obj[valueParam].key);\n            }\n          }\n        }\n      });\n    }\n  }\n  /**\n   * Updates the link data after editing the text\n   * @param {GojsLinkNode} linkData - The link data containing the cardinality values and other link properties.\n   * @memberof GojsCardinalityService\n   */\n  updateLinkOnTextEdited(linkData) {\n    let idLinkType;\n    const linkTypes = this.cardinalityService.getLinkTypes();\n    for (let [key, value] of linkTypes.entries()) {\n      if (value.from == linkData.cardinalityFrom && value.to == linkData.cardinalityTo) {\n        idLinkType = key;\n        break;\n      }\n    }\n    if (idLinkType) {\n      this.updateLink({\n        ...linkData,\n        idLinkType: idLinkType,\n        id: linkData.key\n      });\n    }\n  }\n  /**\n   * Updates the link data and modifies the link in the cardinality service.\n   * @private\n   * @param {go.ObjectData} linkData  - The data of the link to be updated, which includes properties like `id`, `name`, and `idLinkType`.\n   * @memberof GojsCardinalityService\n   */\n  updateLink(linkData) {\n    this.cardinalityService.updateLink({\n      id: linkData['id'],\n      name: linkData['name'],\n      idLinkType: linkData['idLinkType'],\n      color: linkData['color'],\n      fromComment: linkData['fromComment'] ?? null,\n      toComment: linkData['toComment'] ?? null,\n      idDestinationTempClass: linkData['idDestinationTempClass'],\n      idSourceTempClass: linkData['idSourceTempClass'],\n      segmentOffset: linkData['segmentOffset'],\n      linkPort: {\n        idDiagram: this.currentDiagram.id,\n        destinationPort: linkData['toPort'],\n        sourcePort: linkData['fromPort'],\n        idLink: linkData['id'],\n        segmentOffset: linkData['segmentOffset']\n      }\n    }).subscribe(link => {\n      const modifiedLink = this.cardinalityService.getLinkById(link.id);\n      modifiedLink.name = link.name;\n      modifiedLink.idLinkType = link.idLinkType;\n      modifiedLink.fromComment = link.fromComment;\n      modifiedLink.toComment = link.toComment;\n      modifiedLink.linkPorts = modifiedLink.linkPorts.map(port => {\n        if (port.idDiagram === this.currentDiagram.id) {\n          port.sourcePort = link.linkPort.sourcePort;\n          port.destinationPort = link.linkPort.destinationPort;\n          port.segmentOffset = link.linkPort.segmentOffset;\n        }\n        return port;\n      });\n      this.cardinalityService.modifyLink(modifiedLink);\n      this.propertyService.setPropertyData(linkData);\n    });\n  }\n  /**\n   * Handles the case when a deleted link is found.\n   * @param {DeletedLink} deletedLink - The deleted link object.\n   * @param {GojsLinkNode} link - The current link object.\n   * @param {CardinalityDetails} newLink - The new link object.\n   * @param {go.ObjectData} fromNode - The source node.\n   * @param {go.ObjectData} toNode - The destination node.\n   */\n  handleDeletedLink(deletedLink, link, newLink, fromNode, toNode, diagram) {\n    this.cardinalityService.removeLinkHistory(deletedLink.id);\n    const createdCardinality = {\n      ...newLink,\n      id: deletedLink.idLink,\n      linkPort: {\n        sourcePort: newLink.sourcePort,\n        destinationPort: newLink.destinationPort,\n        idDiagram: deletedLink.idDiagram,\n        idLink: deletedLink.idLink,\n        segmentOffset: '0 0'\n      }\n    };\n    this.cardinalityService.createLinkPort(createdCardinality.linkPort).subscribe(linkPortResult => {\n      this.cardinalityService.addNewLinkPort(createdCardinality.id, linkPortResult);\n      this.updateLinkProperties(link, createdCardinality, fromNode, toNode, diagram);\n      diagram.model.addNodeData({\n        key: `${deletedLink.idLink}_${GojsNodeCategory.LinkLabel}`,\n        category: GojsNodeCategory.LinkLabel,\n        idLink: deletedLink.idLink,\n        editable: this.hasEditAccess\n      });\n      const linkToLink = this.cardinalityService.getLinkToLinks().find(ltl => ltl.idLink == deletedLink.idLink);\n      if (linkToLink) {\n        this.createLinkToLinkForExistingLink(linkToLink.idAssociativeClass, diagram);\n      }\n      diagram.updateAllRelationshipsFromData();\n    });\n    this.diagramUtils.removeDeletedLink(deletedLink.id);\n  }\n  /**\n   * Updates the properties of a link in the diagram, including its name, key, category, link type,\n   * @private\n   * @param {GojsLinkNode} link - The existing link node to be updated.\n   * @param {CreatedCardinality} linkData - The updated cardinality data for the link.\n   * @param {go.ObjectData} fromNode - The source node for the link.\n   * @param {go.ObjectData} toNode - The target node for the link.\n   * @param {go.Diagram} diagram - The GoJS diagram where the link resides.\n   * @memberof GojsCardinalityService\n   */\n  updateLinkProperties(link, linkData, fromNode, toNode, diagram) {\n    link.category = GojsNodeCategory.Association;\n    link.key = linkData.id;\n    link.idLinkType = linkData.idLinkType;\n    link.idSourceTempClass = linkData.idSourceTempClass;\n    link.idDestinationTempClass = linkData.idDestinationTempClass;\n    link.idFromClass = fromNode['id'];\n    link.idToClass = toNode['id'];\n    link.name = linkData.name;\n    link.cardinalityFrom = this.cardinalityService.getLinkTypes().get(1)?.from;\n    link.cardinalityTo = this.cardinalityService.getLinkTypes().get(1)?.to;\n    link.fromPort = linkData.linkPort.sourcePort;\n    link.toPort = linkData.linkPort.destinationPort;\n    link.editable = this.hasEditAccess;\n    link.color = linkData.color;\n    link.labelKeys = [`${linkData.id}_${GojsNodeCategory.LinkLabel}`];\n    link.fromComment = linkData.fromComment;\n    link.toComment = linkData.toComment;\n    link.segmentOffset = linkData.linkPort.segmentOffset;\n    diagram.model.updateTargetBindings(link);\n  }\n  updateLinkToLinkProp(link, linkData, diagram) {\n    link.category = GojsNodeCategory.LinkToLink;\n    link.key = linkData.id;\n    link.idLink = linkData.idLink;\n    link.idAssociativeClass = linkData.idAssociativeClass;\n    link.fromPort = linkData.port;\n    link.editable = this.hasEditAccess;\n    diagram.model.updateTargetBindings(link);\n  }\n  /**\n   * Creates a new link based on the provided cardinality data, adds the link to the system,\n   * @param {GojsLinkNode} link - The existing link node to be updated after creating the new link.\n   * @param {CardinalityCreate} newLink - The data required to create the new link, including cardinality information.\n   * @param {go.ObjectData} fromNode - The source node for the new link.\n   * @param {go.ObjectData} toNode - The target node for the new link.\n   * @param {go.Diagram} diagram - The GoJS diagram where the link should be updated.\n   * @memberof GojsCardinalityService\n   */\n  createNewLinkAndUpdate(link, newLink, fromNode, toNode, diagram) {\n    this.cardinalityService.createNewLink(newLink).subscribe(createdLink => {\n      this.cardinalityService.addLink({\n        ...createdLink,\n        linkPorts: [createdLink.linkPort],\n        idFromClass: link.idFromClass,\n        idToClass: link.idToClass,\n        segmentOffset: link.segmentOffset\n      });\n      this.propertyService.setPropertyData(null);\n      this.updateLinkProperties(link, createdLink, fromNode, toNode, diagram);\n      const linkLabelNode = {\n        key: `${createdLink.id}_${GojsNodeCategory.LinkLabel}`,\n        category: GojsNodeCategory.LinkLabel,\n        idLink: createdLink.id,\n        editable: this.hasEditAccess\n      };\n      diagram.model.addNodeData(linkLabelNode);\n      diagram.updateAllRelationshipsFromData();\n    });\n  }\n  createNewLinkToLinkAndUpdate(link, newLink, diagram) {\n    this.cardinalityService.createLinkToLink(newLink).subscribe(createdLink => {\n      this.cardinalityService.addLinkToLink(createdLink);\n      this.updateLinkToLinkProp(link, createdLink, diagram);\n    });\n  }\n  updatePortForLinkToLink(linkObj) {\n    // update port for link to link\n    this.cardinalityService.updateLinkToLink({\n      id: linkObj.key,\n      port: linkObj.fromPort,\n      idAssociativeClass: linkObj.idAssociativeClass,\n      idLink: +linkObj.to.split('_')[0]\n    });\n  }\n  /**\n   * Handles the redrawing of a link by updating its link port properties in the diagram.\n   * @param {GojsLinkNode} linkData - The link data that contains the new port information.\n   * @memberof GojsCardinalityService\n   */\n  handleReDrawnLink(linkData, diagram) {\n    const linkDetails = this.cardinalityService.getLinkById(+linkData.key);\n    const fromNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.from);\n    const toNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.to);\n    if (linkDetails && fromNode && toNode) {\n      const currentDiagramLinkPort = linkDetails.linkPorts.find(port => port.idDiagram === this.currentDiagram.id);\n      if (currentDiagramLinkPort) this.updateLinkPort({\n        ...linkDetails,\n        idDestinationTempClass: toNode['idTemplateClass'],\n        idSourceTempClass: fromNode['idTemplateClass']\n      }, currentDiagramLinkPort, linkData.fromPort, linkData.toPort);\n    }\n  }\n  /**\n   * Updates the link port properties in the diagram for a given link.\n   * @private\n   * @param {CardinalityDetails} linkDetails  - The details of the link being updated, including the link ports.\n   * @param {LinkPort} currentDiagramLinkPort - The link port that corresponds to the current diagram.\n   * @param {string} fromPort - The source port for the link.\n   * @param {string} toPort - The destination port for the link.\n   * @memberof GojsCardinalityService\n   */\n  updateLinkPort(linkDetails, currentDiagramLinkPort, fromPort, toPort) {\n    const linkToUpdate = {\n      id: linkDetails.id,\n      name: linkDetails.name,\n      idLinkType: linkDetails.idLinkType,\n      color: linkDetails.color,\n      fromComment: linkDetails.fromComment,\n      toComment: linkDetails.toComment,\n      idDestinationTempClass: linkDetails.idDestinationTempClass,\n      idSourceTempClass: linkDetails.idSourceTempClass,\n      segmentOffset: currentDiagramLinkPort?.segmentOffset ?? '0 0',\n      linkPort: {\n        ...currentDiagramLinkPort,\n        sourcePort: fromPort,\n        destinationPort: toPort\n      }\n    };\n    const otherDiagramLinkPorts = linkDetails.linkPorts.filter(port => port.idDiagram !== this.currentDiagram.id);\n    this.cardinalityService.updateLink(linkToUpdate).subscribe(response => {\n      this.cardinalityService.modifyLink({\n        ...linkDetails,\n        linkPorts: [...otherDiagramLinkPorts, response.linkPort]\n      });\n    });\n  }\n  /**\n   * Formats and filters the link data into a structure suitable for GoJS link nodes.\n   * @param {ClassEntityDTO[]} classes - An array of class entities that provide the source and destination class details.\n   * @param {DeletedLink[]} linkHistories - An array of deleted link histories to filter out the deleted links.\n   * @return {GojsLinkNode[]} - An array of formatted GoJS link nodes based on the provided classes and links.\n   * @memberof GojsCardinalityService\n   */\n  formatLinkData(classes, linkHistories, diagramId) {\n    const linkTypes = this.cardinalityService.getLinkTypes();\n    const associationLinkData = [];\n    if (classes.length > 0) {\n      this.cardinalityService.getLinks().forEach(link => {\n        const fromClass = classes.find(cls => (cls.id == link.idFromClass || cls.idTemplateClass == link.idSourceTempClass) && cls.isAssociative == false);\n        const toClass = classes.find(cls => (cls.id == link?.idToClass || cls.idTemplateClass == link.idDestinationTempClass) && cls.isAssociative == false);\n        const linkType = linkTypes.get(link.idLinkType);\n        const isDeletedLink = linkHistories.find(deletedLink => deletedLink.idSourceClass == fromClass?.id && deletedLink.idDestinationClass == toClass?.id && deletedLink.idLink === link.id) || {};\n        if (fromClass && toClass && Object.keys(isDeletedLink).length == 0) {\n          const currentDiagramLinkPort = this.getOrCreateLinkPort(link, diagramId);\n          const linkNodeData = {\n            from: `${fromClass?.key?.toString()}`,\n            to: `${toClass?.key?.toString()}`,\n            name: link.name,\n            cardinalityFrom: linkType?.from,\n            cardinalityTo: linkType?.to,\n            key: link.id,\n            id: link.id,\n            idLinkType: link.idLinkType,\n            idSourceTempClass: link.idSourceTempClass,\n            idDestinationTempClass: link.idDestinationTempClass,\n            idFromClass: fromClass?.id,\n            idToClass: toClass?.id,\n            category: GojsNodeCategory.Association,\n            editable: this.hasEditAccess,\n            fromPort: currentDiagramLinkPort?.sourcePort,\n            toPort: currentDiagramLinkPort?.destinationPort,\n            color: link.color,\n            labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\n            fromComment: link.fromComment,\n            toComment: link.toComment,\n            segmentOffset: link.linkPorts.find(port => port.idDiagram == diagramId)?.segmentOffset ?? '0 0' // Offset the label slightly from the midpoint\n          };\n          associationLinkData.push(linkNodeData);\n        }\n      });\n    }\n    return associationLinkData;\n  }\n  generateLinkToLinkNodes(associationLinks, classes) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const processedIds = new Set();\n      const linkToLinks = _this.cardinalityService.getLinkToLinks();\n      const linkToLinkNodes = [];\n      for (const classNode of classes) {\n        if (!classNode.isAssociative) continue;\n        const linkToLink = linkToLinks.find(link => link.idAssociativeClass === classNode.idTemplateClass);\n        if (linkToLink) {\n          const linkNode = associationLinks.find(link => link.id === linkToLink.idLink);\n          if (!linkNode) continue;\n          if (processedIds.has(linkToLink.idLink)) continue;\n          processedIds.add(linkToLink.idLink);\n          linkToLinkNodes.push(_this.formatLinkToLinkData(linkToLink, classNode.key.toString()));\n        }\n      }\n      return linkToLinkNodes;\n    })();\n  }\n  /**\n   * Retrieves an existing link port for the current diagram or creates a new one if it doesn't exist.\n   * @private\n   * @param {CardinalityDetails} link - The link whose port is being retrieved or created.\n   * @return {LinkPort} The existing or newly created link port associated with the current diagram.\n   * @memberof GojsCardinalityService\n   */\n  getOrCreateLinkPort(link, diagramId) {\n    let currentDiagramLinkPort = link.linkPorts?.find(port => port.idDiagram === diagramId);\n    const linkPort = link.linkPorts ? {\n      idDiagram: diagramId,\n      idLink: link.id,\n      sourcePort: link.linkPorts[0]?.sourcePort ? link.linkPorts[0]?.sourcePort : DefaultSourcePort,\n      destinationPort: link.linkPorts[0]?.destinationPort ? link.linkPorts[0]?.destinationPort : DefaultDestinationPort,\n      segmentOffset: link.linkPorts[0]?.segmentOffset ?? '0 0'\n    } : {\n      idDiagram: diagramId,\n      idLink: link.id,\n      sourcePort: link.fromPort ?? DefaultSourcePort,\n      destinationPort: link.toPort ?? DefaultDestinationPort,\n      segmentOffset: '0 0'\n    };\n    if (currentDiagramLinkPort) {\n      return currentDiagramLinkPort;\n    } else {\n      const ports = link.linkPorts ? link.linkPorts : [];\n      this.cardinalityService.createLinkPort(linkPort).subscribe(createdLinkPort => {\n        currentDiagramLinkPort = createdLinkPort;\n        this.cardinalityService.modifyLink({\n          ...link,\n          linkPorts: [...ports, currentDiagramLinkPort]\n        });\n      });\n      return currentDiagramLinkPort ?? linkPort;\n    }\n  }\n  /**\n   * Creates links for a specific class in the GoJS diagram based on its palette ID.\n   * @param {number} idPalette  - The ID of the class in the palette to create links for.\n   * @param {go.Diagram} gojsDiagram - The GoJS diagram where the links should be created.\n   * @memberof GojsCardinalityService\n   */\n  createLinksForPaletteClass(idPalette, gojsDiagram) {\n    if (gojsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] == idPalette).length == 1 && this.cardinalityService.getLinks().find(link => link.idSourceTempClass == idPalette || link.idDestinationTempClass == idPalette)) {\n      const links = this.getRelevantLinks(idPalette);\n      links.forEach(link => {\n        this.createLinkForClass(link, gojsDiagram);\n      });\n    }\n    this.createLinkToLinkForExistingLink(idPalette, gojsDiagram);\n  }\n  createLinkToLinkForExistingLink(idPalette, gojsDiagram) {\n    //For add the link to link node in diagram during drop library\n    if (gojsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] == idPalette && node['category'] == GojsNodeCategory.AssociativeClass).length == 1 && this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == idPalette)) {\n      const linkToLink = this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == idPalette);\n      const isPresentLinkToLink = gojsDiagram.model.linkDataArray.some(link => link['idLink'] == linkToLink?.idLink);\n      if (linkToLink && !isPresentLinkToLink) this.formatLinkToLinkObj(linkToLink, gojsDiagram);\n    }\n  }\n  /**\n   * Retrieves the relevant links associated with a specific class from the palette\n   * @param {number} idPalette - The ID of the class in the palette to create links for.\n   * @return   {CardinalityDetails[]} - An array of relevant links associated with the class.\n   * @memberof GojsCardinalityService\n   */\n  getRelevantLinks(idPalette) {\n    return this.cardinalityService.getLinks().filter(linkData => linkData.idDestinationTempClass === idPalette || linkData.idSourceTempClass === idPalette);\n  }\n  /**\n   * Creates a link between two nodes in the GoJS diagram based on the provided link data.\n   * @private\n   * @param {CardinalityDetails} link - The link data containing source and destination class IDs.\n   * @param {go.Diagram} goJsDiagram - The GoJS diagram where the link should be created.\n   * @memberof GojsCardinalityService\n   */\n  createLinkForClass(link, goJsDiagram) {\n    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idSourceTempClass);\n    const destLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idDestinationTempClass);\n    if (srcLink && destLink) {\n      const linkData = this.mapLinkData(link, srcLink, destLink);\n      goJsDiagram.model.addNodeData({\n        key: `${linkData.id}_${GojsNodeCategory.LinkLabel}`,\n        category: GojsNodeCategory.LinkLabel,\n        idLink: linkData.id,\n        editable: linkData.editable\n      });\n      const linkToLinkNode = this.cardinalityService.getLinkToLinks().find(link => link.idLink == linkData.id);\n      if (linkToLinkNode) this.formatLinkToLinkObj(linkToLinkNode, goJsDiagram);\n      goJsDiagram.updateAllRelationshipsFromData();\n      goJsDiagram.model.addLinkData(linkData);\n    }\n  }\n  formatLinkToLinkObj(link, goJsDiagram) {\n    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idAssociativeClass);\n    if (srcLink) {\n      const linkData = {\n        from: srcLink['key'],\n        key: link.id,\n        idLink: link.idLink,\n        to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\n        idAssociativeClass: link.idAssociativeClass,\n        category: GojsNodeCategory.LinkToLink,\n        editable: this.hasEditAccess,\n        fromPort: link.port\n      };\n      goJsDiagram.model.addLinkData(linkData);\n    }\n  }\n  /**\n   * Removes all links related to the specified palette ID.\n   * @param {number} idPalette - The ID of the palette whose related links should be removed.\n   * @memberof GojsCardinalityService\n   */\n  removeRelatedLinks(idPalette) {\n    const relatedLinks = this.cardinalityService.getLinks().filter(link => link.idDestinationTempClass === idPalette || link.idSourceTempClass === idPalette);\n    this.cardinalityService.removeLinks(relatedLinks);\n  }\n  /**\n   * Removes a link from all diagrams associated with the provided link data.\n   * @param {GojsLinkNode} linkData - The link data to be removed.\n   * @memberof GojsCardinalityService\n   */\n  removeLinkFromAllDiagram(linkData) {\n    this.cardinalityService.removeLink(+linkData.key);\n    this.cardinalityService.delete(+linkData.key);\n    this.diagramUtils.removeDeletedLink(+linkData.key);\n    this.cardinalityService.removeLinkToLinkByProperty('idLink', +linkData.key);\n  }\n  /**\n   * Removes a link from the current diagram, with an option for temporary or permanent removal.\n   * @param {GojsLinkNode} linkData - The link data to be removed.\n   * @param {go.Diagram} diagram  - The GoJS diagram from which the link is to be removed.\n   * @param {boolean} isPermanent - Flag to indicate if the removal is permanent.\n   * @memberof GojsCardinalityService\n   */\n  removeLinkFromCurrentDiagram(linkData, diagram, isPermanent) {\n    if (!isPermanent) this.cardinalityService.createDeletedLinkHistory({\n      idDiagram: this.currentDiagram.id,\n      idSourceClass: linkData.idFromClass,\n      idDestinationClass: linkData.idToClass,\n      idLink: +linkData.key\n    });\n    const linkDetails = this.cardinalityService.getLinkById(+linkData.key);\n    const currentDiagramLinkPort = linkDetails.linkPorts.find(port => port.idDiagram === this.currentDiagram.id);\n    if (currentDiagramLinkPort) {\n      if (!isPermanent) this.cardinalityService.deleteLinkPort(currentDiagramLinkPort.id);\n      this.cardinalityService.removeExistingLinkPort(linkDetails.id, currentDiagramLinkPort.id);\n    }\n    diagram.model.removeLinkData(linkData);\n  }\n  updateLinkFromProperty(updatedNode, diagram) {\n    let link = null;\n    diagram.model.linkDataArray.forEach(linkData => {\n      if (linkData['key'] === updatedNode.key && linkData['category'] === updatedNode.category) {\n        link = linkData;\n      }\n    });\n    if (!link) return;\n    const linkData = link;\n    this.gojsCommonService.setDataProperties(diagram.model, linkData, {\n      name: updatedNode.name,\n      fromComment: updatedNode.fromComment,\n      toComment: updatedNode.toComment,\n      color: updatedNode.color\n    });\n    const modifiedLink = this.cardinalityService.getLinkById(updatedNode.id);\n    modifiedLink.name = updatedNode.name;\n    modifiedLink.fromComment = updatedNode.fromComment;\n    modifiedLink.toComment = updatedNode.toComment;\n    modifiedLink.color = updatedNode.color;\n    this.cardinalityService.modifyLink(modifiedLink);\n  }\n  formatLinkToLinkData(link, fromClassKey) {\n    const linkToLinkNode = {\n      from: fromClassKey,\n      to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\n      key: link.id,\n      idLink: link.idLink,\n      idAssociativeClass: link.idAssociativeClass,\n      category: GojsNodeCategory.LinkToLink,\n      editable: this.hasEditAccess,\n      fromPort: link.port\n    };\n    return linkToLinkNode;\n  }\n  static #_ = this.ɵfac = function GojsCardinalityService_Factory(t) {\n    return new (t || GojsCardinalityService)(i0.ɵɵinject(i1.CardinalityService), i0.ɵɵinject(i2.AccessService), i0.ɵɵinject(i3.MatDialog), i0.ɵɵinject(i4.DiagramUtils), i0.ɵɵinject(i5.PropertyService), i0.ɵɵinject(i6.GojsCommonService), i0.ɵɵinject(i7.SnackBarService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsCardinalityService,\n    factory: GojsCardinalityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["DialogConfirmationComponent", "GojsNodeCategory", "AccessType", "DefaultDestinationPort", "DefaultSourcePort", "GojsCardinalityService", "constructor", "cardinalityService", "accessService", "dialog", "diagramUtils", "propertyService", "gojsCommonService", "snackbarService", "hasEditAccess", "accessTypeChanges", "subscribe", "response", "Viewer", "activeDiagramChanges", "diagram", "currentDiagram", "validateLink", "fromNode", "toNode", "linkRelations", "linkData", "fromPort", "gojsDiagram", "data", "linkDataArray", "JSON", "parse", "model", "to<PERSON><PERSON>", "idTemplateClass", "id", "category", "AssociativeClass", "some", "link", "relation", "find", "from", "to", "includes", "Object", "keys", "length", "LinkToLink", "validateFromNodeLinkToLink", "validateLinkToLink", "linkToLinks", "getLinkToLinks", "isLinkAlreadyExists", "idLink", "info", "validateGroupLink", "Class", "LinkLabel", "mapLinkData", "srcLink", "destLink", "name", "cardinalityFrom", "getLinkTypes", "get", "idLinkType", "cardinalityTo", "key", "idSourceTempClass", "idDestinationTempClass", "idFromClass", "idToClass", "Association", "editable", "linkPorts", "sourcePort", "to<PERSON><PERSON>", "destinationPort", "color", "labelKeys", "fromComment", "toComment", "segmentOffset", "createLinkPort", "idDiagram", "createdLinkPort", "addNewLinkPort", "deleteLink", "node", "nodeCount", "event", "dialogRef", "open", "width", "title", "reject", "confirm", "afterClosed", "isConfirm", "performLinkDeletion", "removeLinkData", "removeLinkFromCurrentDiagram", "removeLinkFromAllDiagram", "handleUndoRedoLinking", "action", "isDelete", "object", "each", "obj", "valueParam", "removeLinkToLinkByProperty", "deleteLinkToLink", "removeLink", "delete", "addLinkToLink", "undoLinkToLinkDeletion", "addLink", "undoLinkDeletion", "updateLinkOnTextEdited", "linkTypes", "value", "entries", "updateLink", "linkPort", "modifiedLink", "getLinkById", "map", "port", "modifyLink", "setPropertyData", "handleDeletedLink", "deletedLink", "newLink", "removeLinkHistory", "createdCardinality", "linkPortResult", "updateLinkProperties", "addNodeData", "linkToLink", "ltl", "createLinkToLinkForExistingLink", "idAssociativeClass", "updateAllRelationshipsFromData", "removeDeletedLink", "updateTargetBindings", "updateLinkToLinkProp", "createNewLinkAndUpdate", "createNewLink", "createdLink", "linkLabelNode", "createNewLinkToLinkAndUpdate", "createLinkToLink", "updatePortForLinkToLink", "linkObj", "updateLinkToLink", "split", "handleReDrawnLink", "linkDetails", "getObjectDataByKey", "currentDiagramLinkPort", "updateLinkPort", "linkToUpdate", "otherDiagramLinkPorts", "filter", "formatLinkData", "classes", "linkHistories", "diagramId", "associationLinkData", "getLinks", "for<PERSON>ach", "fromClass", "cls", "isAssociative", "toClass", "linkType", "isDeletedLink", "idSourceClass", "idDestinationClass", "getOrCreateLinkPort", "linkNodeData", "toString", "push", "generateLinkToLinkNodes", "associationLinks", "_this", "_asyncToGenerator", "processedIds", "Set", "linkToLinkNodes", "classNode", "linkNode", "has", "add", "formatLinkToLinkData", "ports", "createLinksForPaletteClass", "idPalette", "nodeDataArray", "links", "getRelevantLinks", "createLinkForClass", "isPresentLinkToLink", "formatLinkToLinkObj", "goJsDiagram", "findNodeByIdTemplateClass", "linkToLinkNode", "addLinkData", "removeRelatedLinks", "relatedLinks", "removeLinks", "isPermanent", "createDeletedLinkHistory", "deleteLinkPort", "removeExistingLinkPort", "updateLinkFromProperty", "updatedNode", "setDataProperties", "fromClassKey", "_", "i0", "ɵɵinject", "i1", "CardinalityService", "i2", "AccessService", "i3", "MatDialog", "i4", "DiagramUtils", "i5", "PropertyService", "i6", "GojsCommonService", "i7", "SnackBarService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitCodeBassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojsCardinality\\gojs-cardinality.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { GraphLinksModel } from 'gojs';\r\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\r\nimport {\r\n  CardinalityCreate,\r\n  CardinalityDetails,\r\n  CardinalityPatch,\r\n  CreatedCardinality,\r\n  DeletedLink,\r\n  LinkPort,\r\n  LinkToLink,\r\n} from 'src/app/shared/model/cardinality';\r\nimport { ClassEntityDTO } from 'src/app/shared/model/class';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { ConfirmDialogData } from 'src/app/shared/model/dialog';\r\nimport {\r\n  GojsLinkNode,\r\n  GoJsLinkToLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport {\r\n  DefaultDestinationPort,\r\n  DefaultSourcePort,\r\n} from 'src/app/shared/utils/constants';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../../access/access.service';\r\nimport { CardinalityService } from '../../cardinality/cardinality.service';\r\nimport { PropertyService } from '../../property/property.service';\r\nimport { SnackBarService } from '../../snackbar/snack-bar.service';\r\nimport { GojsCommonService } from '../gojsCommon/gojs-common.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsCardinalityService {\r\n  private hasEditAccess: boolean = false;\r\n  private currentDiagram!: Diagram;\r\n  constructor(\r\n    private cardinalityService: CardinalityService,\r\n    private accessService: AccessService,\r\n    private dialog: MatDialog,\r\n    private diagramUtils: DiagramUtils,\r\n    private propertyService: PropertyService,\r\n    private gojsCommonService: GojsCommonService,\r\n    private snackbarService: SnackBarService\r\n  ) {\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      this.hasEditAccess = response != AccessType.Viewer;\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Validates whether a link between two nodes in a GoJS diagram is allowed based on predefined link relations and existing links.\r\n   * @param fromNode - The starting node of the link.\r\n   * @param toNode - The target node of the link.\r\n   * @param linkRelations - An array defining allowed relations between node categories.\r\n   * @param linkData - The data object representing the link being validated.\r\n   * @param gojsDiagram - The GoJS diagram instance containing the nodes and links.\r\n   * @returns A boolean indicating whether the link is valid.\r\n   */\r\n  validateLink(\r\n    fromNode: go.Node,\r\n    toNode: go.Node,\r\n    linkRelations: { from: GojsNodeCategory; to: GojsNodeCategory[] }[],\r\n    linkData: go.Link,\r\n    fromPort: string,\r\n    gojsDiagram: go.Diagram\r\n  ): boolean {\r\n    if (!fromNode?.data || !toNode?.data) return false;\r\n    const linkDataArray = JSON.parse(gojsDiagram.model.toJson())[\r\n      'linkDataArray'\r\n    ];\r\n    // To Prevent link between classes from same template class\r\n    if (\r\n      fromNode.data.idTemplateClass == toNode.data.idTemplateClass &&\r\n      fromNode.data.id != toNode.data.id\r\n    )\r\n      return false;\r\n    //Condition for changing the toPort of associative class and preventing to draw multiple link for same associative class\r\n    if (\r\n      fromNode.data.category == GojsNodeCategory.AssociativeClass &&\r\n      linkDataArray.some(\r\n        (link: go.ObjectData) =>\r\n          link['idAssociativeClass'] &&\r\n          link['idAssociativeClass'] === fromNode.data.idTemplateClass\r\n      ) &&\r\n      linkData == null\r\n    ) {\r\n      return false;\r\n    }\r\n    // Find the relation that matches the category of the fromNode\r\n    const relation = linkRelations?.find(\r\n      (link) => link.from === fromNode.data.category\r\n    );\r\n\r\n    // If no relation exists for the fromNode category, the link is invalid\r\n    if (!relation) return false;\r\n\r\n    // Check if the toNode category is allowed by the relation\r\n    if (!relation.to.includes(toNode.data.category)) return false;\r\n    // If the linkData is provided, check for specific cardinality rules\r\n    if (linkData && Object.keys(linkData).length > 0) {\r\n      if (linkData.category === GojsNodeCategory.LinkToLink) {\r\n        return this.validateFromNodeLinkToLink(\r\n          linkData,\r\n          fromNode,\r\n          toNode,\r\n          fromPort\r\n        );\r\n      } else {\r\n        return true;\r\n        // const cardinalityLinks = this.cardinalityService.getLinks();\r\n        // const isCardinalityValid = cardinalityLinks.some(\r\n        //   (link) =>\r\n        //     link.idSourceTempClass === fromNode.data.idTemplateClass ||\r\n        //     link.idDestinationTempClass === toNode.data.idTemplateClass\r\n        // );\r\n        // const isLinkAlreadyExists = linkDataArray.some(\r\n        //   (link: GojsLinkNode) =>\r\n        //     link.idFromClass === fromNode.data.id &&\r\n        //     link.idToClass === toNode.data.id\r\n        // );\r\n        // return isCardinalityValid && isLinkAlreadyExists;\r\n      }\r\n    } else if (fromNode.data.category == GojsNodeCategory.AssociativeClass) {\r\n      return this.validateLinkToLink(toNode);\r\n    }\r\n    // If all checks pass, the link is valid\r\n    return true;\r\n  }\r\n\r\n  validateLinkToLink(toNode: go.Node) {\r\n    const linkToLinks = this.cardinalityService.getLinkToLinks();\r\n    const isLinkAlreadyExists = linkToLinks.some(\r\n      (link) => link.idLink === toNode.data.idLink\r\n    );\r\n    if (isLinkAlreadyExists) {\r\n      this.snackbarService.info('snackBar.linkToLinkAlreadyExists');\r\n    }\r\n    return !isLinkAlreadyExists;\r\n  }\r\n  // For checking the link to link from node port change is valid or not\r\n  validateFromNodeLinkToLink(\r\n    linkData: go.Link,\r\n    fromNode: go.Node,\r\n    toNode: go.Node,\r\n    fromPort: string\r\n  ) {\r\n    const linkToLinks = this.cardinalityService.getLinkToLinks();\r\n    const isLinkAlreadyExists = linkToLinks.some(\r\n      (link) => link.idLink === toNode.data.idLink\r\n    );\r\n    if (linkData.data.fromPort !== fromPort) {\r\n      return (\r\n        linkData.data['idAssociativeClass'] === fromNode.data.idTemplateClass\r\n      );\r\n    }\r\n    return !isLinkAlreadyExists;\r\n  }\r\n\r\n  /**\r\n   * Validates the relationship between two nodes (fromNode and toNode) based on the link's relation data.\r\n   * @param {go.Node} fromNode - The source node of the link to be validated.\r\n   * @param {go.Node} toNode - The target node of the link to be validated.\r\n   * @param {go.Link} link - The link to be validated.\r\n   * @param {go.Diagram} gojsDiagram - The diagram to which the link and nodes belong.\r\n   * @return {*}  {boolean} - Returns `true` if the link is valid based on the relations, otherwise returns `false`.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  validateGroupLink(\r\n    fromNode: go.Node,\r\n    toNode: go.Node,\r\n    link: go.Link,\r\n    fromPort: string,\r\n    gojsDiagram: go.Diagram\r\n  ): boolean {\r\n    if (link && link.data && link.data.linkRelations) {\r\n      return this.validateLink(\r\n        fromNode,\r\n        toNode,\r\n        link.data.linkRelations,\r\n        link,\r\n        fromPort,\r\n        gojsDiagram\r\n      );\r\n    } else {\r\n      const linkRelations = [\r\n        {\r\n          from: GojsNodeCategory.Class,\r\n          to: [GojsNodeCategory.Class],\r\n        },\r\n        {\r\n          from: GojsNodeCategory.AssociativeClass,\r\n          to: [GojsNodeCategory.LinkLabel],\r\n        },\r\n      ];\r\n      return this.validateLink(\r\n        fromNode,\r\n        toNode,\r\n        linkRelations,\r\n        link,\r\n        fromPort,\r\n        gojsDiagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Maps link data to the format required by the diagram.\r\n   * @param {CardinalityDetails} link The link data.\r\n   * @param {go.ObjectData} srcLink The source node data.\r\n   * @param {go.ObjectData} destLink The destination node data.\r\n   * @returns {GojsLinkNode} The mapped link data.\r\n   * @memberOf CardinalityService\r\n   */\r\n  mapLinkData(\r\n    link: CardinalityDetails,\r\n    srcLink: go.ObjectData,\r\n    destLink: go.ObjectData\r\n  ): GojsLinkNode {\r\n    let linkData = {\r\n      from: srcLink['key'],\r\n      to: destLink['key'],\r\n      name: link.name,\r\n      cardinalityFrom: this.cardinalityService\r\n        .getLinkTypes()\r\n        .get(link.idLinkType)?.from!,\r\n      cardinalityTo: this.cardinalityService.getLinkTypes().get(link.idLinkType)\r\n        ?.to!,\r\n      key: link.id!,\r\n      id: link.id!,\r\n      idLinkType: link.idLinkType,\r\n      idSourceTempClass: link.idSourceTempClass,\r\n      idDestinationTempClass: link.idDestinationTempClass,\r\n      idFromClass: srcLink['id'],\r\n      idToClass: destLink['id'],\r\n      category: GojsNodeCategory.Association,\r\n      editable: this.hasEditAccess,\r\n      fromPort: link.linkPorts[0]?.sourcePort,\r\n      toPort: link.linkPorts[0]?.destinationPort,\r\n      color: link.color,\r\n      labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\r\n      fromComment: link.fromComment,\r\n      toComment: link.toComment,\r\n      segmentOffset: link.linkPorts[0]?.segmentOffset,\r\n    };\r\n    this.cardinalityService\r\n      .createLinkPort({\r\n        idLink: link.id!,\r\n        idDiagram: this.currentDiagram.id!,\r\n        sourcePort: link.linkPorts[0]?.sourcePort ?? 'R2',\r\n        destinationPort: link.linkPorts[0]?.destinationPort ?? 'L2',\r\n        segmentOffset: '0 0',\r\n      })\r\n      .subscribe((createdLinkPort) => {\r\n        this.cardinalityService.addNewLinkPort(link.id!, createdLinkPort);\r\n      });\r\n    return linkData;\r\n  }\r\n\r\n  /**\r\n   * Deletes a link based on the node's data.\r\n   * @param {go.Node} node - The node containing the link to be deleted.\r\n   * @param {number} nodeCount - The number of links associated with the node.\r\n   * @param {go.Diagram} diagram - The diagram containing the node and link to be deleted.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  deleteLink(\r\n    node: go.Node,\r\n    nodeCount: number,\r\n    diagram: go.Diagram,\r\n    event: go.DiagramEvent\r\n  ): void {\r\n    const linkData: GojsLinkNode = node.data;\r\n\r\n    // Always show confirmation dialog for link deletion\r\n    const dialogRef = this.dialog.open<\r\n      DialogConfirmationComponent,\r\n      ConfirmDialogData,\r\n      boolean\r\n    >(DialogConfirmationComponent, {\r\n      width: '320px',\r\n      data: {\r\n        title: 'dialog.deleteTitle',\r\n        reject: 'dialog.no',\r\n        confirm: 'dialog.yes',\r\n      },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((isConfirm) => {\r\n      if (isConfirm) {\r\n        // User confirmed deletion - manually remove from diagram and handle business logic\r\n        this.performLinkDeletion(linkData, diagram, nodeCount);\r\n      }\r\n      // If user cancels (isConfirm is false), do nothing - link remains in diagram\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Performs the actual link deletion after confirmation\r\n   * @private\r\n   * @param {GojsLinkNode} linkData - The link data to be deleted\r\n   * @param {go.Diagram} diagram - The diagram containing the link\r\n   * @param {number} nodeCount - The number of nodes selected\r\n   */\r\n  private performLinkDeletion(\r\n    linkData: GojsLinkNode,\r\n    diagram: go.Diagram,\r\n    nodeCount: number\r\n  ): void {\r\n    // Manually remove the link from the diagram model\r\n    (diagram.model as go.GraphLinksModel).removeLinkData(linkData);\r\n\r\n    // Handle business logic based on selection count\r\n    if (nodeCount > 1) {\r\n      // Multiple nodes selected, remove link from current diagram only\r\n      this.removeLinkFromCurrentDiagram(linkData, diagram, true);\r\n    } else {\r\n      // Single node selected, remove link from all diagrams\r\n      this.removeLinkFromAllDiagram(linkData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles linking operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  handleUndoRedoLinking(\r\n    event: go.ChangedEvent,\r\n    action: string,\r\n    isDelete: boolean\r\n  ): void {\r\n    if (event.object) {\r\n      event.object['changes']['iterator'].each((obj: go.ObjectData) => {\r\n        if (obj['propertyName'] == 'linkDataArray') {\r\n          const valueParam = isDelete ? 'oldValue' : 'newValue';\r\n          if (action === 'undo') {\r\n            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\r\n              this.cardinalityService.removeLinkToLinkByProperty(\r\n                'id',\r\n                obj[valueParam].key\r\n              );\r\n              this.cardinalityService.deleteLinkToLink(obj[valueParam].key);\r\n            } else {\r\n              this.cardinalityService.removeLink(obj[valueParam].key);\r\n              this.cardinalityService.delete(obj[valueParam].key);\r\n            }\r\n          } else {\r\n            if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\r\n              this.cardinalityService.addLinkToLink(obj[valueParam]);\r\n              this.cardinalityService.undoLinkToLinkDeletion(\r\n                obj[valueParam].key\r\n              );\r\n            } else {\r\n              this.cardinalityService.addLink(obj[valueParam]);\r\n              this.cardinalityService.undoLinkDeletion(obj[valueParam].key);\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the link data after editing the text\r\n   * @param {GojsLinkNode} linkData - The link data containing the cardinality values and other link properties.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  updateLinkOnTextEdited(linkData: GojsLinkNode): void {\r\n    let idLinkType;\r\n    const linkTypes = this.cardinalityService.getLinkTypes();\r\n    for (let [key, value] of linkTypes.entries()) {\r\n      if (\r\n        value.from == linkData.cardinalityFrom &&\r\n        value.to == linkData.cardinalityTo\r\n      ) {\r\n        idLinkType = key;\r\n        break;\r\n      }\r\n    }\r\n    if (idLinkType) {\r\n      this.updateLink({\r\n        ...linkData,\r\n        idLinkType: idLinkType,\r\n        id: linkData.key,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the link data and modifies the link in the cardinality service.\r\n   * @private\r\n   * @param {go.ObjectData} linkData  - The data of the link to be updated, which includes properties like `id`, `name`, and `idLinkType`.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  private updateLink(linkData: go.ObjectData) {\r\n    this.cardinalityService\r\n      .updateLink({\r\n        id: linkData['id'],\r\n        name: linkData['name'],\r\n        idLinkType: linkData['idLinkType'],\r\n        color: linkData['color'],\r\n        fromComment: linkData['fromComment'] ?? null,\r\n        toComment: linkData['toComment'] ?? null,\r\n        idDestinationTempClass: linkData['idDestinationTempClass'],\r\n        idSourceTempClass: linkData['idSourceTempClass'],\r\n        segmentOffset: linkData['segmentOffset'],\r\n        linkPort: {\r\n          idDiagram: this.currentDiagram.id!,\r\n          destinationPort: linkData['toPort']!,\r\n          sourcePort: linkData['fromPort']!,\r\n          idLink: linkData['id']!,\r\n          segmentOffset: linkData['segmentOffset'],\r\n        },\r\n      })\r\n      .subscribe((link) => {\r\n        const modifiedLink = this.cardinalityService.getLinkById(link.id!);\r\n        modifiedLink.name = link.name;\r\n        modifiedLink.idLinkType = link.idLinkType;\r\n        modifiedLink.fromComment = link.fromComment;\r\n        modifiedLink.toComment = link.toComment;\r\n        modifiedLink.linkPorts = modifiedLink.linkPorts.map((port) => {\r\n          if (port.idDiagram === this.currentDiagram.id) {\r\n            port.sourcePort = link.linkPort.sourcePort;\r\n            port.destinationPort = link.linkPort.destinationPort;\r\n            port.segmentOffset = link.linkPort.segmentOffset;\r\n          }\r\n          return port;\r\n        });\r\n        this.cardinalityService.modifyLink(modifiedLink);\r\n        this.propertyService.setPropertyData(linkData as GojsLinkNode);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Handles the case when a deleted link is found.\r\n   * @param {DeletedLink} deletedLink - The deleted link object.\r\n   * @param {GojsLinkNode} link - The current link object.\r\n   * @param {CardinalityDetails} newLink - The new link object.\r\n   * @param {go.ObjectData} fromNode - The source node.\r\n   * @param {go.ObjectData} toNode - The destination node.\r\n   */\r\n  handleDeletedLink(\r\n    deletedLink: DeletedLink,\r\n    link: GojsLinkNode,\r\n    newLink: CardinalityCreate,\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData,\r\n    diagram: go.Diagram\r\n  ): void {\r\n    this.cardinalityService.removeLinkHistory(deletedLink.id!);\r\n    const createdCardinality: CreatedCardinality = {\r\n      ...newLink,\r\n      id: deletedLink.idLink!,\r\n      linkPort: {\r\n        sourcePort: newLink.sourcePort,\r\n        destinationPort: newLink.destinationPort,\r\n        idDiagram: deletedLink.idDiagram,\r\n        idLink: deletedLink.idLink,\r\n        segmentOffset: '0 0',\r\n      },\r\n    };\r\n    this.cardinalityService\r\n      .createLinkPort(createdCardinality.linkPort)\r\n      .subscribe((linkPortResult) => {\r\n        this.cardinalityService.addNewLinkPort(\r\n          createdCardinality.id!,\r\n          linkPortResult\r\n        );\r\n        this.updateLinkProperties(\r\n          link,\r\n          createdCardinality,\r\n          fromNode,\r\n          toNode,\r\n          diagram\r\n        );\r\n        diagram.model.addNodeData({\r\n          key: `${deletedLink.idLink}_${GojsNodeCategory.LinkLabel}`,\r\n          category: GojsNodeCategory.LinkLabel,\r\n          idLink: deletedLink.idLink,\r\n          editable: this.hasEditAccess,\r\n        });\r\n        const linkToLink = this.cardinalityService\r\n          .getLinkToLinks()\r\n          .find((ltl) => ltl.idLink == deletedLink.idLink);\r\n\r\n        if (linkToLink) {\r\n          this.createLinkToLinkForExistingLink(\r\n            linkToLink.idAssociativeClass,\r\n            diagram\r\n          );\r\n        }\r\n        diagram.updateAllRelationshipsFromData();\r\n      });\r\n\r\n    this.diagramUtils.removeDeletedLink(deletedLink.id!);\r\n  }\r\n\r\n  /**\r\n   * Updates the properties of a link in the diagram, including its name, key, category, link type,\r\n   * @private\r\n   * @param {GojsLinkNode} link - The existing link node to be updated.\r\n   * @param {CreatedCardinality} linkData - The updated cardinality data for the link.\r\n   * @param {go.ObjectData} fromNode - The source node for the link.\r\n   * @param {go.ObjectData} toNode - The target node for the link.\r\n   * @param {go.Diagram} diagram - The GoJS diagram where the link resides.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  private updateLinkProperties(\r\n    link: GojsLinkNode,\r\n    linkData: CreatedCardinality,\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData,\r\n    diagram: go.Diagram\r\n  ): void {\r\n    link.category = GojsNodeCategory.Association;\r\n    link.key = linkData.id!;\r\n    link.idLinkType = linkData.idLinkType;\r\n    link.idSourceTempClass = linkData.idSourceTempClass;\r\n    link.idDestinationTempClass = linkData.idDestinationTempClass;\r\n    link.idFromClass = fromNode['id'];\r\n    link.idToClass = toNode['id'];\r\n    link.name = linkData.name;\r\n    link.cardinalityFrom = this.cardinalityService.getLinkTypes().get(1)?.from!;\r\n    link.cardinalityTo = this.cardinalityService.getLinkTypes().get(1)?.to!;\r\n    link.fromPort = linkData.linkPort.sourcePort;\r\n    link.toPort = linkData.linkPort.destinationPort;\r\n    link.editable = this.hasEditAccess;\r\n    link.color = linkData.color;\r\n    link.labelKeys = [`${linkData.id}_${GojsNodeCategory.LinkLabel}`];\r\n    link.fromComment = linkData.fromComment;\r\n    link.toComment = linkData.toComment;\r\n    link.segmentOffset = linkData.linkPort.segmentOffset;\r\n    diagram.model.updateTargetBindings(link);\r\n  }\r\n  private updateLinkToLinkProp(\r\n    link: GoJsLinkToLinkNode,\r\n    linkData: LinkToLink,\r\n    diagram: go.Diagram\r\n  ) {\r\n    link.category = GojsNodeCategory.LinkToLink;\r\n    link.key = linkData.id!;\r\n    link.idLink = linkData.idLink;\r\n    link.idAssociativeClass = linkData.idAssociativeClass;\r\n    link.fromPort = linkData.port;\r\n    link.editable = this.hasEditAccess;\r\n    diagram.model.updateTargetBindings(link);\r\n  }\r\n\r\n  /**\r\n   * Creates a new link based on the provided cardinality data, adds the link to the system,\r\n   * @param {GojsLinkNode} link - The existing link node to be updated after creating the new link.\r\n   * @param {CardinalityCreate} newLink - The data required to create the new link, including cardinality information.\r\n   * @param {go.ObjectData} fromNode - The source node for the new link.\r\n   * @param {go.ObjectData} toNode - The target node for the new link.\r\n   * @param {go.Diagram} diagram - The GoJS diagram where the link should be updated.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  createNewLinkAndUpdate(\r\n    link: GojsLinkNode,\r\n    newLink: CardinalityCreate,\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData,\r\n    diagram: go.Diagram\r\n  ): void {\r\n    this.cardinalityService.createNewLink(newLink).subscribe((createdLink) => {\r\n      this.cardinalityService.addLink({\r\n        ...createdLink,\r\n        linkPorts: [createdLink.linkPort],\r\n        idFromClass: link.idFromClass,\r\n        idToClass: link.idToClass,\r\n        segmentOffset: link.segmentOffset,\r\n      });\r\n      this.propertyService.setPropertyData(null);\r\n      this.updateLinkProperties(link, createdLink, fromNode, toNode, diagram);\r\n      const linkLabelNode = {\r\n        key: `${createdLink.id}_${GojsNodeCategory.LinkLabel}`,\r\n        category: GojsNodeCategory.LinkLabel,\r\n        idLink: createdLink.id,\r\n        editable: this.hasEditAccess,\r\n      };\r\n      diagram.model.addNodeData(linkLabelNode);\r\n      diagram.updateAllRelationshipsFromData();\r\n    });\r\n  }\r\n  createNewLinkToLinkAndUpdate(\r\n    link: GoJsLinkToLinkNode,\r\n    newLink: LinkToLink,\r\n    diagram: go.Diagram\r\n  ): void {\r\n    this.cardinalityService\r\n      .createLinkToLink(newLink)\r\n      .subscribe((createdLink) => {\r\n        this.cardinalityService.addLinkToLink(createdLink);\r\n        this.updateLinkToLinkProp(link, createdLink, diagram);\r\n      });\r\n  }\r\n\r\n  updatePortForLinkToLink(linkObj: GoJsLinkToLinkNode) {\r\n    // update port for link to link\r\n    this.cardinalityService.updateLinkToLink({\r\n      id: linkObj.key,\r\n      port: linkObj.fromPort,\r\n      idAssociativeClass: linkObj.idAssociativeClass,\r\n      idLink: +linkObj.to.split('_')[0],\r\n    });\r\n  }\r\n  /**\r\n   * Handles the redrawing of a link by updating its link port properties in the diagram.\r\n   * @param {GojsLinkNode} linkData - The link data that contains the new port information.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  handleReDrawnLink(linkData: GojsLinkNode, diagram: go.Diagram): void {\r\n    const linkDetails = this.cardinalityService.getLinkById(+linkData.key!);\r\n    const fromNode = this.diagramUtils.getObjectDataByKey(\r\n      diagram,\r\n      linkData.from\r\n    );\r\n    const toNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.to);\r\n    if (linkDetails && fromNode && toNode) {\r\n      const currentDiagramLinkPort = linkDetails.linkPorts.find(\r\n        (port) => port.idDiagram === this.currentDiagram.id\r\n      );\r\n      if (currentDiagramLinkPort)\r\n        this.updateLinkPort(\r\n          {\r\n            ...linkDetails,\r\n            idDestinationTempClass: toNode['idTemplateClass'],\r\n            idSourceTempClass: fromNode['idTemplateClass'],\r\n          },\r\n          currentDiagramLinkPort,\r\n          linkData.fromPort!,\r\n          linkData.toPort!\r\n        );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the link port properties in the diagram for a given link.\r\n   * @private\r\n   * @param {CardinalityDetails} linkDetails  - The details of the link being updated, including the link ports.\r\n   * @param {LinkPort} currentDiagramLinkPort - The link port that corresponds to the current diagram.\r\n   * @param {string} fromPort - The source port for the link.\r\n   * @param {string} toPort - The destination port for the link.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  private updateLinkPort(\r\n    linkDetails: CardinalityDetails,\r\n    currentDiagramLinkPort: LinkPort,\r\n    fromPort: string,\r\n    toPort: string\r\n  ) {\r\n    const linkToUpdate: CardinalityPatch = {\r\n      id: linkDetails.id,\r\n      name: linkDetails.name,\r\n      idLinkType: linkDetails.idLinkType,\r\n      color: linkDetails.color,\r\n      fromComment: linkDetails.fromComment,\r\n      toComment: linkDetails.toComment,\r\n      idDestinationTempClass: linkDetails.idDestinationTempClass,\r\n      idSourceTempClass: linkDetails.idSourceTempClass,\r\n      segmentOffset: currentDiagramLinkPort?.segmentOffset ?? '0 0',\r\n      linkPort: {\r\n        ...currentDiagramLinkPort,\r\n        sourcePort: fromPort,\r\n        destinationPort: toPort,\r\n      },\r\n    };\r\n    const otherDiagramLinkPorts = linkDetails.linkPorts.filter(\r\n      (port) => port.idDiagram !== this.currentDiagram.id\r\n    );\r\n    this.cardinalityService.updateLink(linkToUpdate).subscribe((response) => {\r\n      this.cardinalityService.modifyLink({\r\n        ...linkDetails,\r\n        linkPorts: [...otherDiagramLinkPorts, response.linkPort],\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Formats and filters the link data into a structure suitable for GoJS link nodes.\r\n   * @param {ClassEntityDTO[]} classes - An array of class entities that provide the source and destination class details.\r\n   * @param {DeletedLink[]} linkHistories - An array of deleted link histories to filter out the deleted links.\r\n   * @return {GojsLinkNode[]} - An array of formatted GoJS link nodes based on the provided classes and links.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  formatLinkData(\r\n    classes: ClassEntityDTO[],\r\n    linkHistories: DeletedLink[],\r\n    diagramId: number\r\n  ): GojsLinkNode[] {\r\n    const linkTypes = this.cardinalityService.getLinkTypes();\r\n    const associationLinkData: GojsLinkNode[] = [];\r\n    if (classes.length > 0) {\r\n      this.cardinalityService.getLinks().forEach((link) => {\r\n        const fromClass = classes.find(\r\n          (cls) =>\r\n            (cls.id == link.idFromClass ||\r\n              cls.idTemplateClass == link.idSourceTempClass) &&\r\n            cls.isAssociative == false\r\n        );\r\n        const toClass = classes.find(\r\n          (cls) =>\r\n            (cls.id == link?.idToClass ||\r\n              cls.idTemplateClass == link.idDestinationTempClass) &&\r\n            cls.isAssociative == false\r\n        );\r\n        const linkType = linkTypes.get(link.idLinkType);\r\n        const isDeletedLink =\r\n          linkHistories.find(\r\n            (deletedLink) =>\r\n              deletedLink.idSourceClass == fromClass?.id &&\r\n              deletedLink.idDestinationClass == toClass?.id &&\r\n              deletedLink.idLink === link.id\r\n          ) || {};\r\n\r\n        if (fromClass && toClass && Object.keys(isDeletedLink).length == 0) {\r\n          const currentDiagramLinkPort = this.getOrCreateLinkPort(\r\n            link,\r\n            diagramId\r\n          );\r\n          const linkNodeData: GojsLinkNode = {\r\n            from: `${fromClass?.key?.toString()!}`,\r\n            to: `${toClass?.key?.toString()!}`,\r\n            name: link.name,\r\n            cardinalityFrom: linkType?.from!,\r\n            cardinalityTo: linkType?.to!,\r\n            key: link.id!,\r\n            id: link.id!,\r\n            idLinkType: link.idLinkType,\r\n            idSourceTempClass: link.idSourceTempClass,\r\n            idDestinationTempClass: link.idDestinationTempClass,\r\n            idFromClass: fromClass?.id!,\r\n            idToClass: toClass?.id!,\r\n            category: GojsNodeCategory.Association,\r\n            editable: this.hasEditAccess,\r\n            fromPort: currentDiagramLinkPort?.sourcePort,\r\n            toPort: currentDiagramLinkPort?.destinationPort,\r\n            color: link.color,\r\n            labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\r\n            fromComment: link.fromComment,\r\n            toComment: link.toComment,\r\n            segmentOffset:\r\n              link.linkPorts.find((port) => port.idDiagram == diagramId)\r\n                ?.segmentOffset ?? '0 0', // Offset the label slightly from the midpoint\r\n          };\r\n          associationLinkData.push(linkNodeData);\r\n        }\r\n      });\r\n    }\r\n    return associationLinkData;\r\n  }\r\n\r\n  async generateLinkToLinkNodes(\r\n    associationLinks: GojsLinkNode[],\r\n    classes: ClassEntityDTO[]\r\n  ): Promise<GoJsLinkToLinkNode[]> {\r\n    const processedIds = new Set<number>();\r\n    const linkToLinks = this.cardinalityService.getLinkToLinks();\r\n    const linkToLinkNodes: GoJsLinkToLinkNode[] = [];\r\n    for (const classNode of classes) {\r\n      if (!classNode.isAssociative) continue;\r\n      const linkToLink = linkToLinks.find(\r\n        (link) => link.idAssociativeClass === classNode.idTemplateClass\r\n      );\r\n      if (linkToLink) {\r\n        const linkNode = associationLinks.find(\r\n          (link) => link.id === linkToLink.idLink\r\n        );\r\n        if (!linkNode) continue;\r\n        if (processedIds.has(linkToLink.idLink)) continue;\r\n        processedIds.add(linkToLink.idLink);\r\n        linkToLinkNodes.push(\r\n          this.formatLinkToLinkData(linkToLink, classNode.key!.toString())\r\n        );\r\n      }\r\n    }\r\n    return linkToLinkNodes;\r\n  }\r\n  /**\r\n   * Retrieves an existing link port for the current diagram or creates a new one if it doesn't exist.\r\n   * @private\r\n   * @param {CardinalityDetails} link - The link whose port is being retrieved or created.\r\n   * @return {LinkPort} The existing or newly created link port associated with the current diagram.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  private getOrCreateLinkPort(\r\n    link: CardinalityDetails,\r\n    diagramId: number\r\n  ): LinkPort {\r\n    let currentDiagramLinkPort = link.linkPorts?.find(\r\n      (port) => port.idDiagram === diagramId\r\n    );\r\n    const linkPort: LinkPort = link.linkPorts\r\n      ? {\r\n          idDiagram: diagramId,\r\n          idLink: link.id!,\r\n          sourcePort: link.linkPorts[0]?.sourcePort\r\n            ? link.linkPorts[0]?.sourcePort\r\n            : DefaultSourcePort,\r\n          destinationPort: link.linkPorts[0]?.destinationPort\r\n            ? link.linkPorts[0]?.destinationPort\r\n            : DefaultDestinationPort,\r\n          segmentOffset: link.linkPorts[0]?.segmentOffset ?? '0 0',\r\n        }\r\n      : {\r\n          idDiagram: diagramId,\r\n          idLink: link.id!,\r\n          sourcePort:\r\n            (link as unknown as GojsLinkNode).fromPort ?? DefaultSourcePort,\r\n          destinationPort:\r\n            (link as unknown as GojsLinkNode).toPort ?? DefaultDestinationPort,\r\n          segmentOffset: '0 0',\r\n        };\r\n    if (currentDiagramLinkPort) {\r\n      return currentDiagramLinkPort;\r\n    } else {\r\n      const ports = link.linkPorts ? link.linkPorts : [];\r\n      this.cardinalityService\r\n        .createLinkPort(linkPort)\r\n        .subscribe((createdLinkPort) => {\r\n          currentDiagramLinkPort = createdLinkPort;\r\n          this.cardinalityService.modifyLink({\r\n            ...link,\r\n            linkPorts: [...ports, currentDiagramLinkPort],\r\n          });\r\n        });\r\n      return currentDiagramLinkPort ?? linkPort;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates links for a specific class in the GoJS diagram based on its palette ID.\r\n   * @param {number} idPalette  - The ID of the class in the palette to create links for.\r\n   * @param {go.Diagram} gojsDiagram - The GoJS diagram where the links should be created.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  createLinksForPaletteClass(idPalette: number, gojsDiagram: go.Diagram) {\r\n    if (\r\n      gojsDiagram.model.nodeDataArray.filter(\r\n        (node) => node['idTemplateClass'] == idPalette\r\n      ).length == 1 &&\r\n      this.cardinalityService\r\n        .getLinks()\r\n        .find(\r\n          (link) =>\r\n            link.idSourceTempClass == idPalette ||\r\n            link.idDestinationTempClass == idPalette\r\n        )\r\n    ) {\r\n      const links = this.getRelevantLinks(idPalette);\r\n      links.forEach((link) => {\r\n        this.createLinkForClass(link, gojsDiagram);\r\n      });\r\n    }\r\n    this.createLinkToLinkForExistingLink(idPalette, gojsDiagram);\r\n  }\r\n\r\n  createLinkToLinkForExistingLink(idPalette: number, gojsDiagram: go.Diagram) {\r\n    //For add the link to link node in diagram during drop library\r\n    if (\r\n      gojsDiagram.model.nodeDataArray.filter(\r\n        (node) =>\r\n          node['idTemplateClass'] == idPalette &&\r\n          node['category'] == GojsNodeCategory.AssociativeClass\r\n      ).length == 1 &&\r\n      this.cardinalityService\r\n        .getLinkToLinks()\r\n        .find((link) => link.idAssociativeClass == idPalette)\r\n    ) {\r\n      const linkToLink = this.cardinalityService\r\n        .getLinkToLinks()\r\n        .find((link) => link.idAssociativeClass == idPalette);\r\n      const isPresentLinkToLink = (\r\n        gojsDiagram.model as go.GraphLinksModel\r\n      ).linkDataArray.some((link) => link['idLink'] == linkToLink?.idLink);\r\n      if (linkToLink && !isPresentLinkToLink)\r\n        this.formatLinkToLinkObj(linkToLink, gojsDiagram);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retrieves the relevant links associated with a specific class from the palette\r\n   * @param {number} idPalette - The ID of the class in the palette to create links for.\r\n   * @return   {CardinalityDetails[]} - An array of relevant links associated with the class.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  getRelevantLinks(idPalette: number): CardinalityDetails[] {\r\n    return this.cardinalityService\r\n      .getLinks()\r\n      .filter(\r\n        (linkData) =>\r\n          linkData.idDestinationTempClass === idPalette ||\r\n          linkData.idSourceTempClass === idPalette\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Creates a link between two nodes in the GoJS diagram based on the provided link data.\r\n   * @private\r\n   * @param {CardinalityDetails} link - The link data containing source and destination class IDs.\r\n   * @param {go.Diagram} goJsDiagram - The GoJS diagram where the link should be created.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  private createLinkForClass(\r\n    link: CardinalityDetails,\r\n    goJsDiagram: go.Diagram\r\n  ) {\r\n    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(\r\n      goJsDiagram,\r\n      link.idSourceTempClass\r\n    );\r\n    const destLink = this.diagramUtils.findNodeByIdTemplateClass(\r\n      goJsDiagram,\r\n      link.idDestinationTempClass\r\n    );\r\n    if (srcLink && destLink) {\r\n      const linkData = this.mapLinkData(link, srcLink, destLink);\r\n      goJsDiagram.model.addNodeData({\r\n        key: `${linkData.id}_${GojsNodeCategory.LinkLabel}`,\r\n        category: GojsNodeCategory.LinkLabel,\r\n        idLink: linkData.id,\r\n        editable: linkData.editable,\r\n      });\r\n      const linkToLinkNode = this.cardinalityService\r\n        .getLinkToLinks()\r\n        .find((link) => link.idLink == linkData.id);\r\n      if (linkToLinkNode) this.formatLinkToLinkObj(linkToLinkNode, goJsDiagram);\r\n      goJsDiagram.updateAllRelationshipsFromData();\r\n      (goJsDiagram.model as go.GraphLinksModel).addLinkData(linkData);\r\n    }\r\n  }\r\n  private formatLinkToLinkObj(link: LinkToLink, goJsDiagram: go.Diagram) {\r\n    const srcLink = this.diagramUtils.findNodeByIdTemplateClass(\r\n      goJsDiagram,\r\n      link.idAssociativeClass\r\n    );\r\n    if (srcLink) {\r\n      const linkData: GoJsLinkToLinkNode = {\r\n        from: srcLink['key'],\r\n        key: link.id!,\r\n        idLink: link.idLink,\r\n        to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\r\n        idAssociativeClass: link.idAssociativeClass,\r\n        category: GojsNodeCategory.LinkToLink,\r\n        editable: this.hasEditAccess,\r\n        fromPort: link.port,\r\n      };\r\n      (goJsDiagram.model as go.GraphLinksModel).addLinkData(linkData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes all links related to the specified palette ID.\r\n   * @param {number} idPalette - The ID of the palette whose related links should be removed.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  removeRelatedLinks(idPalette: number) {\r\n    const relatedLinks = this.cardinalityService\r\n      .getLinks()\r\n      .filter(\r\n        (link) =>\r\n          link.idDestinationTempClass === idPalette ||\r\n          link.idSourceTempClass === idPalette\r\n      );\r\n    this.cardinalityService.removeLinks(relatedLinks);\r\n  }\r\n\r\n  /**\r\n   * Removes a link from all diagrams associated with the provided link data.\r\n   * @param {GojsLinkNode} linkData - The link data to be removed.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  removeLinkFromAllDiagram(linkData: GojsLinkNode): void {\r\n    this.cardinalityService.removeLink(+linkData.key!);\r\n    this.cardinalityService.delete(+linkData.key!);\r\n    this.diagramUtils.removeDeletedLink(+linkData.key!);\r\n    this.cardinalityService.removeLinkToLinkByProperty(\r\n      'idLink',\r\n      +linkData.key!\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Removes a link from the current diagram, with an option for temporary or permanent removal.\r\n   * @param {GojsLinkNode} linkData - The link data to be removed.\r\n   * @param {go.Diagram} diagram  - The GoJS diagram from which the link is to be removed.\r\n   * @param {boolean} isPermanent - Flag to indicate if the removal is permanent.\r\n   * @memberof GojsCardinalityService\r\n   */\r\n  removeLinkFromCurrentDiagram(\r\n    linkData: GojsLinkNode,\r\n    diagram: go.Diagram,\r\n    isPermanent: boolean\r\n  ): void {\r\n    if (!isPermanent)\r\n      this.cardinalityService.createDeletedLinkHistory({\r\n        idDiagram: this.currentDiagram.id!,\r\n        idSourceClass: linkData.idFromClass,\r\n        idDestinationClass: linkData.idToClass,\r\n        idLink: +linkData.key!,\r\n      });\r\n    const linkDetails = this.cardinalityService.getLinkById(+linkData.key!);\r\n    const currentDiagramLinkPort = linkDetails.linkPorts.find(\r\n      (port) => port.idDiagram === this.currentDiagram.id\r\n    );\r\n    if (currentDiagramLinkPort) {\r\n      if (!isPermanent)\r\n        this.cardinalityService.deleteLinkPort(currentDiagramLinkPort.id!);\r\n      this.cardinalityService.removeExistingLinkPort(\r\n        linkDetails.id!,\r\n        currentDiagramLinkPort.id!\r\n      );\r\n    }\r\n    (diagram.model as GraphLinksModel).removeLinkData(linkData);\r\n  }\r\n\r\n  updateLinkFromProperty(updatedNode: GojsLinkNode, diagram: go.Diagram): void {\r\n    let link: go.ObjectData | null = null;\r\n    (diagram.model as GraphLinksModel).linkDataArray.forEach((linkData) => {\r\n      if (\r\n        linkData['key'] === updatedNode.key &&\r\n        linkData['category'] === updatedNode.category\r\n      ) {\r\n        link = linkData;\r\n      }\r\n    });\r\n    if (!link) return;\r\n\r\n    const linkData = link;\r\n    this.gojsCommonService.setDataProperties(diagram.model, linkData, {\r\n      name: updatedNode.name,\r\n      fromComment: updatedNode.fromComment,\r\n      toComment: updatedNode.toComment,\r\n      color: updatedNode.color,\r\n    });\r\n    const modifiedLink = this.cardinalityService.getLinkById(updatedNode.id!);\r\n    modifiedLink.name = updatedNode.name;\r\n    modifiedLink.fromComment = updatedNode.fromComment;\r\n    modifiedLink.toComment = updatedNode.toComment;\r\n    modifiedLink.color = updatedNode.color;\r\n    this.cardinalityService.modifyLink(modifiedLink);\r\n  }\r\n\r\n  formatLinkToLinkData(link: LinkToLink, fromClassKey: string) {\r\n    const linkToLinkNode: GoJsLinkToLinkNode = {\r\n      from: fromClassKey,\r\n      to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\r\n      key: link.id!,\r\n      idLink: link.idLink,\r\n      idAssociativeClass: link.idAssociativeClass,\r\n      category: GojsNodeCategory.LinkToLink,\r\n      editable: this.hasEditAccess,\r\n      fromPort: link.port,\r\n    };\r\n    return linkToLinkNode;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,SAASA,2BAA2B,QAAQ,2EAA2E;AAavH,SAGEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SACEC,sBAAsB,EACtBC,iBAAiB,QACZ,gCAAgC;;;;;;;;;AAUvC,OAAM,MAAOC,sBAAsB;EAGjCC,YACUC,kBAAsC,EACtCC,aAA4B,EAC5BC,MAAiB,EACjBC,YAA0B,EAC1BC,eAAgC,EAChCC,iBAAoC,EACpCC,eAAgC;IANhC,KAAAN,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IATjB,KAAAC,aAAa,GAAY,KAAK;IAWpC,IAAI,CAACN,aAAa,CAACO,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC5D,IAAI,CAACH,aAAa,GAAGG,QAAQ,IAAIf,UAAU,CAACgB,MAAM;IACpD,CAAC,CAAC;IACF,IAAI,CAACR,YAAY,CAACS,oBAAoB,EAAE,CAACH,SAAS,CAAEI,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEA;;;;;;;;;EASAE,YAAYA,CACVC,QAAiB,EACjBC,MAAe,EACfC,aAAmE,EACnEC,QAAiB,EACjBC,QAAgB,EAChBC,WAAuB;IAEvB,IAAI,CAACL,QAAQ,EAAEM,IAAI,IAAI,CAACL,MAAM,EAAEK,IAAI,EAAE,OAAO,KAAK;IAClD,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACJ,WAAW,CAACK,KAAK,CAACC,MAAM,EAAE,CAAC,CAC1D,eAAe,CAChB;IACD;IACA,IACEX,QAAQ,CAACM,IAAI,CAACM,eAAe,IAAIX,MAAM,CAACK,IAAI,CAACM,eAAe,IAC5DZ,QAAQ,CAACM,IAAI,CAACO,EAAE,IAAIZ,MAAM,CAACK,IAAI,CAACO,EAAE,EAElC,OAAO,KAAK;IACd;IACA,IACEb,QAAQ,CAACM,IAAI,CAACQ,QAAQ,IAAIpC,gBAAgB,CAACqC,gBAAgB,IAC3DR,aAAa,CAACS,IAAI,CACfC,IAAmB,IAClBA,IAAI,CAAC,oBAAoB,CAAC,IAC1BA,IAAI,CAAC,oBAAoB,CAAC,KAAKjB,QAAQ,CAACM,IAAI,CAACM,eAAe,CAC/D,IACDT,QAAQ,IAAI,IAAI,EAChB;MACA,OAAO,KAAK;;IAEd;IACA,MAAMe,QAAQ,GAAGhB,aAAa,EAAEiB,IAAI,CACjCF,IAAI,IAAKA,IAAI,CAACG,IAAI,KAAKpB,QAAQ,CAACM,IAAI,CAACQ,QAAQ,CAC/C;IAED;IACA,IAAI,CAACI,QAAQ,EAAE,OAAO,KAAK;IAE3B;IACA,IAAI,CAACA,QAAQ,CAACG,EAAE,CAACC,QAAQ,CAACrB,MAAM,CAACK,IAAI,CAACQ,QAAQ,CAAC,EAAE,OAAO,KAAK;IAC7D;IACA,IAAIX,QAAQ,IAAIoB,MAAM,CAACC,IAAI,CAACrB,QAAQ,CAAC,CAACsB,MAAM,GAAG,CAAC,EAAE;MAChD,IAAItB,QAAQ,CAACW,QAAQ,KAAKpC,gBAAgB,CAACgD,UAAU,EAAE;QACrD,OAAO,IAAI,CAACC,0BAA0B,CACpCxB,QAAQ,EACRH,QAAQ,EACRC,MAAM,EACNG,QAAQ,CACT;OACF,MAAM;QACL,OAAO,IAAI;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;KAEH,MAAM,IAAIJ,QAAQ,CAACM,IAAI,CAACQ,QAAQ,IAAIpC,gBAAgB,CAACqC,gBAAgB,EAAE;MACtE,OAAO,IAAI,CAACa,kBAAkB,CAAC3B,MAAM,CAAC;;IAExC;IACA,OAAO,IAAI;EACb;EAEA2B,kBAAkBA,CAAC3B,MAAe;IAChC,MAAM4B,WAAW,GAAG,IAAI,CAAC7C,kBAAkB,CAAC8C,cAAc,EAAE;IAC5D,MAAMC,mBAAmB,GAAGF,WAAW,CAACb,IAAI,CACzCC,IAAI,IAAKA,IAAI,CAACe,MAAM,KAAK/B,MAAM,CAACK,IAAI,CAAC0B,MAAM,CAC7C;IACD,IAAID,mBAAmB,EAAE;MACvB,IAAI,CAACzC,eAAe,CAAC2C,IAAI,CAAC,kCAAkC,CAAC;;IAE/D,OAAO,CAACF,mBAAmB;EAC7B;EACA;EACAJ,0BAA0BA,CACxBxB,QAAiB,EACjBH,QAAiB,EACjBC,MAAe,EACfG,QAAgB;IAEhB,MAAMyB,WAAW,GAAG,IAAI,CAAC7C,kBAAkB,CAAC8C,cAAc,EAAE;IAC5D,MAAMC,mBAAmB,GAAGF,WAAW,CAACb,IAAI,CACzCC,IAAI,IAAKA,IAAI,CAACe,MAAM,KAAK/B,MAAM,CAACK,IAAI,CAAC0B,MAAM,CAC7C;IACD,IAAI7B,QAAQ,CAACG,IAAI,CAACF,QAAQ,KAAKA,QAAQ,EAAE;MACvC,OACED,QAAQ,CAACG,IAAI,CAAC,oBAAoB,CAAC,KAAKN,QAAQ,CAACM,IAAI,CAACM,eAAe;;IAGzE,OAAO,CAACmB,mBAAmB;EAC7B;EAEA;;;;;;;;;EASAG,iBAAiBA,CACflC,QAAiB,EACjBC,MAAe,EACfgB,IAAa,EACbb,QAAgB,EAChBC,WAAuB;IAEvB,IAAIY,IAAI,IAAIA,IAAI,CAACX,IAAI,IAAIW,IAAI,CAACX,IAAI,CAACJ,aAAa,EAAE;MAChD,OAAO,IAAI,CAACH,YAAY,CACtBC,QAAQ,EACRC,MAAM,EACNgB,IAAI,CAACX,IAAI,CAACJ,aAAa,EACvBe,IAAI,EACJb,QAAQ,EACRC,WAAW,CACZ;KACF,MAAM;MACL,MAAMH,aAAa,GAAG,CACpB;QACEkB,IAAI,EAAE1C,gBAAgB,CAACyD,KAAK;QAC5Bd,EAAE,EAAE,CAAC3C,gBAAgB,CAACyD,KAAK;OAC5B,EACD;QACEf,IAAI,EAAE1C,gBAAgB,CAACqC,gBAAgB;QACvCM,EAAE,EAAE,CAAC3C,gBAAgB,CAAC0D,SAAS;OAChC,CACF;MACD,OAAO,IAAI,CAACrC,YAAY,CACtBC,QAAQ,EACRC,MAAM,EACNC,aAAa,EACbe,IAAI,EACJb,QAAQ,EACRC,WAAW,CACZ;;EAEL;EAEA;;;;;;;;EAQAgC,WAAWA,CACTpB,IAAwB,EACxBqB,OAAsB,EACtBC,QAAuB;IAEvB,IAAIpC,QAAQ,GAAG;MACbiB,IAAI,EAAEkB,OAAO,CAAC,KAAK,CAAC;MACpBjB,EAAE,EAAEkB,QAAQ,CAAC,KAAK,CAAC;MACnBC,IAAI,EAAEvB,IAAI,CAACuB,IAAI;MACfC,eAAe,EAAE,IAAI,CAACzD,kBAAkB,CACrC0D,YAAY,EAAE,CACdC,GAAG,CAAC1B,IAAI,CAAC2B,UAAU,CAAC,EAAExB,IAAK;MAC9ByB,aAAa,EAAE,IAAI,CAAC7D,kBAAkB,CAAC0D,YAAY,EAAE,CAACC,GAAG,CAAC1B,IAAI,CAAC2B,UAAU,CAAC,EACtEvB,EAAG;MACPyB,GAAG,EAAE7B,IAAI,CAACJ,EAAG;MACbA,EAAE,EAAEI,IAAI,CAACJ,EAAG;MACZ+B,UAAU,EAAE3B,IAAI,CAAC2B,UAAU;MAC3BG,iBAAiB,EAAE9B,IAAI,CAAC8B,iBAAiB;MACzCC,sBAAsB,EAAE/B,IAAI,CAAC+B,sBAAsB;MACnDC,WAAW,EAAEX,OAAO,CAAC,IAAI,CAAC;MAC1BY,SAAS,EAAEX,QAAQ,CAAC,IAAI,CAAC;MACzBzB,QAAQ,EAAEpC,gBAAgB,CAACyE,WAAW;MACtCC,QAAQ,EAAE,IAAI,CAAC7D,aAAa;MAC5Ba,QAAQ,EAAEa,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU;MACvCC,MAAM,EAAEtC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEG,eAAe;MAC1CC,KAAK,EAAExC,IAAI,CAACwC,KAAK;MACjBC,SAAS,EAAE,CAAC,GAAGzC,IAAI,CAACJ,EAAE,IAAInC,gBAAgB,CAAC0D,SAAS,EAAE,CAAC;MACvDuB,WAAW,EAAE1C,IAAI,CAAC0C,WAAW;MAC7BC,SAAS,EAAE3C,IAAI,CAAC2C,SAAS;MACzBC,aAAa,EAAE5C,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEQ;KACnC;IACD,IAAI,CAAC7E,kBAAkB,CACpB8E,cAAc,CAAC;MACd9B,MAAM,EAAEf,IAAI,CAACJ,EAAG;MAChBkD,SAAS,EAAE,IAAI,CAACjE,cAAc,CAACe,EAAG;MAClCyC,UAAU,EAAErC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,IAAI,IAAI;MACjDE,eAAe,EAAEvC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEG,eAAe,IAAI,IAAI;MAC3DK,aAAa,EAAE;KAChB,CAAC,CACDpE,SAAS,CAAEuE,eAAe,IAAI;MAC7B,IAAI,CAAChF,kBAAkB,CAACiF,cAAc,CAAChD,IAAI,CAACJ,EAAG,EAAEmD,eAAe,CAAC;IACnE,CAAC,CAAC;IACJ,OAAO7D,QAAQ;EACjB;EAEA;;;;;;;EAOA+D,UAAUA,CACRC,IAAa,EACbC,SAAiB,EACjBvE,OAAmB,EACnBwE,KAAsB;IAEtB,MAAMlE,QAAQ,GAAiBgE,IAAI,CAAC7D,IAAI;IAExC;IACA,MAAMgE,SAAS,GAAG,IAAI,CAACpF,MAAM,CAACqF,IAAI,CAIhC9F,2BAA2B,EAAE;MAC7B+F,KAAK,EAAE,OAAO;MACdlE,IAAI,EAAE;QACJmE,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE;;KAEZ,CAAC;IAEFL,SAAS,CAACM,WAAW,EAAE,CAACnF,SAAS,CAAEoF,SAAS,IAAI;MAC9C,IAAIA,SAAS,EAAE;QACb;QACA,IAAI,CAACC,mBAAmB,CAAC3E,QAAQ,EAAEN,OAAO,EAAEuE,SAAS,CAAC;;MAExD;IACF,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOQU,mBAAmBA,CACzB3E,QAAsB,EACtBN,OAAmB,EACnBuE,SAAiB;IAEjB;IACCvE,OAAO,CAACa,KAA4B,CAACqE,cAAc,CAAC5E,QAAQ,CAAC;IAE9D;IACA,IAAIiE,SAAS,GAAG,CAAC,EAAE;MACjB;MACA,IAAI,CAACY,4BAA4B,CAAC7E,QAAQ,EAAEN,OAAO,EAAE,IAAI,CAAC;KAC3D,MAAM;MACL;MACA,IAAI,CAACoF,wBAAwB,CAAC9E,QAAQ,CAAC;;EAE3C;EAEA;;;;;;;EAOA+E,qBAAqBA,CACnBb,KAAsB,EACtBc,MAAc,EACdC,QAAiB;IAEjB,IAAIf,KAAK,CAACgB,MAAM,EAAE;MAChBhB,KAAK,CAACgB,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAACC,IAAI,CAAEC,GAAkB,IAAI;QAC9D,IAAIA,GAAG,CAAC,cAAc,CAAC,IAAI,eAAe,EAAE;UAC1C,MAAMC,UAAU,GAAGJ,QAAQ,GAAG,UAAU,GAAG,UAAU;UACrD,IAAID,MAAM,KAAK,MAAM,EAAE;YACrB,IAAII,GAAG,CAACC,UAAU,CAAC,CAAC1E,QAAQ,IAAIpC,gBAAgB,CAACgD,UAAU,EAAE;cAC3D,IAAI,CAAC1C,kBAAkB,CAACyG,0BAA0B,CAChD,IAAI,EACJF,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CACpB;cACD,IAAI,CAAC9D,kBAAkB,CAAC0G,gBAAgB,CAACH,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CAAC;aAC9D,MAAM;cACL,IAAI,CAAC9D,kBAAkB,CAAC2G,UAAU,CAACJ,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CAAC;cACvD,IAAI,CAAC9D,kBAAkB,CAAC4G,MAAM,CAACL,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CAAC;;WAEtD,MAAM;YACL,IAAIyC,GAAG,CAACC,UAAU,CAAC,CAAC1E,QAAQ,IAAIpC,gBAAgB,CAACgD,UAAU,EAAE;cAC3D,IAAI,CAAC1C,kBAAkB,CAAC6G,aAAa,CAACN,GAAG,CAACC,UAAU,CAAC,CAAC;cACtD,IAAI,CAACxG,kBAAkB,CAAC8G,sBAAsB,CAC5CP,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CACpB;aACF,MAAM;cACL,IAAI,CAAC9D,kBAAkB,CAAC+G,OAAO,CAACR,GAAG,CAACC,UAAU,CAAC,CAAC;cAChD,IAAI,CAACxG,kBAAkB,CAACgH,gBAAgB,CAACT,GAAG,CAACC,UAAU,CAAC,CAAC1C,GAAG,CAAC;;;;MAIrE,CAAC,CAAC;;EAEN;EAEA;;;;;EAKAmD,sBAAsBA,CAAC9F,QAAsB;IAC3C,IAAIyC,UAAU;IACd,MAAMsD,SAAS,GAAG,IAAI,CAAClH,kBAAkB,CAAC0D,YAAY,EAAE;IACxD,KAAK,IAAI,CAACI,GAAG,EAAEqD,KAAK,CAAC,IAAID,SAAS,CAACE,OAAO,EAAE,EAAE;MAC5C,IACED,KAAK,CAAC/E,IAAI,IAAIjB,QAAQ,CAACsC,eAAe,IACtC0D,KAAK,CAAC9E,EAAE,IAAIlB,QAAQ,CAAC0C,aAAa,EAClC;QACAD,UAAU,GAAGE,GAAG;QAChB;;;IAGJ,IAAIF,UAAU,EAAE;MACd,IAAI,CAACyD,UAAU,CAAC;QACd,GAAGlG,QAAQ;QACXyC,UAAU,EAAEA,UAAU;QACtB/B,EAAE,EAAEV,QAAQ,CAAC2C;OACd,CAAC;;EAEN;EAEA;;;;;;EAMQuD,UAAUA,CAAClG,QAAuB;IACxC,IAAI,CAACnB,kBAAkB,CACpBqH,UAAU,CAAC;MACVxF,EAAE,EAAEV,QAAQ,CAAC,IAAI,CAAC;MAClBqC,IAAI,EAAErC,QAAQ,CAAC,MAAM,CAAC;MACtByC,UAAU,EAAEzC,QAAQ,CAAC,YAAY,CAAC;MAClCsD,KAAK,EAAEtD,QAAQ,CAAC,OAAO,CAAC;MACxBwD,WAAW,EAAExD,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI;MAC5CyD,SAAS,EAAEzD,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI;MACxC6C,sBAAsB,EAAE7C,QAAQ,CAAC,wBAAwB,CAAC;MAC1D4C,iBAAiB,EAAE5C,QAAQ,CAAC,mBAAmB,CAAC;MAChD0D,aAAa,EAAE1D,QAAQ,CAAC,eAAe,CAAC;MACxCmG,QAAQ,EAAE;QACRvC,SAAS,EAAE,IAAI,CAACjE,cAAc,CAACe,EAAG;QAClC2C,eAAe,EAAErD,QAAQ,CAAC,QAAQ,CAAE;QACpCmD,UAAU,EAAEnD,QAAQ,CAAC,UAAU,CAAE;QACjC6B,MAAM,EAAE7B,QAAQ,CAAC,IAAI,CAAE;QACvB0D,aAAa,EAAE1D,QAAQ,CAAC,eAAe;;KAE1C,CAAC,CACDV,SAAS,CAAEwB,IAAI,IAAI;MAClB,MAAMsF,YAAY,GAAG,IAAI,CAACvH,kBAAkB,CAACwH,WAAW,CAACvF,IAAI,CAACJ,EAAG,CAAC;MAClE0F,YAAY,CAAC/D,IAAI,GAAGvB,IAAI,CAACuB,IAAI;MAC7B+D,YAAY,CAAC3D,UAAU,GAAG3B,IAAI,CAAC2B,UAAU;MACzC2D,YAAY,CAAC5C,WAAW,GAAG1C,IAAI,CAAC0C,WAAW;MAC3C4C,YAAY,CAAC3C,SAAS,GAAG3C,IAAI,CAAC2C,SAAS;MACvC2C,YAAY,CAAClD,SAAS,GAAGkD,YAAY,CAAClD,SAAS,CAACoD,GAAG,CAAEC,IAAI,IAAI;QAC3D,IAAIA,IAAI,CAAC3C,SAAS,KAAK,IAAI,CAACjE,cAAc,CAACe,EAAE,EAAE;UAC7C6F,IAAI,CAACpD,UAAU,GAAGrC,IAAI,CAACqF,QAAQ,CAAChD,UAAU;UAC1CoD,IAAI,CAAClD,eAAe,GAAGvC,IAAI,CAACqF,QAAQ,CAAC9C,eAAe;UACpDkD,IAAI,CAAC7C,aAAa,GAAG5C,IAAI,CAACqF,QAAQ,CAACzC,aAAa;;QAElD,OAAO6C,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAAC1H,kBAAkB,CAAC2H,UAAU,CAACJ,YAAY,CAAC;MAChD,IAAI,CAACnH,eAAe,CAACwH,eAAe,CAACzG,QAAwB,CAAC;IAChE,CAAC,CAAC;EACN;EAEA;;;;;;;;EAQA0G,iBAAiBA,CACfC,WAAwB,EACxB7F,IAAkB,EAClB8F,OAA0B,EAC1B/G,QAAuB,EACvBC,MAAqB,EACrBJ,OAAmB;IAEnB,IAAI,CAACb,kBAAkB,CAACgI,iBAAiB,CAACF,WAAW,CAACjG,EAAG,CAAC;IAC1D,MAAMoG,kBAAkB,GAAuB;MAC7C,GAAGF,OAAO;MACVlG,EAAE,EAAEiG,WAAW,CAAC9E,MAAO;MACvBsE,QAAQ,EAAE;QACRhD,UAAU,EAAEyD,OAAO,CAACzD,UAAU;QAC9BE,eAAe,EAAEuD,OAAO,CAACvD,eAAe;QACxCO,SAAS,EAAE+C,WAAW,CAAC/C,SAAS;QAChC/B,MAAM,EAAE8E,WAAW,CAAC9E,MAAM;QAC1B6B,aAAa,EAAE;;KAElB;IACD,IAAI,CAAC7E,kBAAkB,CACpB8E,cAAc,CAACmD,kBAAkB,CAACX,QAAQ,CAAC,CAC3C7G,SAAS,CAAEyH,cAAc,IAAI;MAC5B,IAAI,CAAClI,kBAAkB,CAACiF,cAAc,CACpCgD,kBAAkB,CAACpG,EAAG,EACtBqG,cAAc,CACf;MACD,IAAI,CAACC,oBAAoB,CACvBlG,IAAI,EACJgG,kBAAkB,EAClBjH,QAAQ,EACRC,MAAM,EACNJ,OAAO,CACR;MACDA,OAAO,CAACa,KAAK,CAAC0G,WAAW,CAAC;QACxBtE,GAAG,EAAE,GAAGgE,WAAW,CAAC9E,MAAM,IAAItD,gBAAgB,CAAC0D,SAAS,EAAE;QAC1DtB,QAAQ,EAAEpC,gBAAgB,CAAC0D,SAAS;QACpCJ,MAAM,EAAE8E,WAAW,CAAC9E,MAAM;QAC1BoB,QAAQ,EAAE,IAAI,CAAC7D;OAChB,CAAC;MACF,MAAM8H,UAAU,GAAG,IAAI,CAACrI,kBAAkB,CACvC8C,cAAc,EAAE,CAChBX,IAAI,CAAEmG,GAAG,IAAKA,GAAG,CAACtF,MAAM,IAAI8E,WAAW,CAAC9E,MAAM,CAAC;MAElD,IAAIqF,UAAU,EAAE;QACd,IAAI,CAACE,+BAA+B,CAClCF,UAAU,CAACG,kBAAkB,EAC7B3H,OAAO,CACR;;MAEHA,OAAO,CAAC4H,8BAA8B,EAAE;IAC1C,CAAC,CAAC;IAEJ,IAAI,CAACtI,YAAY,CAACuI,iBAAiB,CAACZ,WAAW,CAACjG,EAAG,CAAC;EACtD;EAEA;;;;;;;;;;EAUQsG,oBAAoBA,CAC1BlG,IAAkB,EAClBd,QAA4B,EAC5BH,QAAuB,EACvBC,MAAqB,EACrBJ,OAAmB;IAEnBoB,IAAI,CAACH,QAAQ,GAAGpC,gBAAgB,CAACyE,WAAW;IAC5ClC,IAAI,CAAC6B,GAAG,GAAG3C,QAAQ,CAACU,EAAG;IACvBI,IAAI,CAAC2B,UAAU,GAAGzC,QAAQ,CAACyC,UAAU;IACrC3B,IAAI,CAAC8B,iBAAiB,GAAG5C,QAAQ,CAAC4C,iBAAiB;IACnD9B,IAAI,CAAC+B,sBAAsB,GAAG7C,QAAQ,CAAC6C,sBAAsB;IAC7D/B,IAAI,CAACgC,WAAW,GAAGjD,QAAQ,CAAC,IAAI,CAAC;IACjCiB,IAAI,CAACiC,SAAS,GAAGjD,MAAM,CAAC,IAAI,CAAC;IAC7BgB,IAAI,CAACuB,IAAI,GAAGrC,QAAQ,CAACqC,IAAI;IACzBvB,IAAI,CAACwB,eAAe,GAAG,IAAI,CAACzD,kBAAkB,CAAC0D,YAAY,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEvB,IAAK;IAC3EH,IAAI,CAAC4B,aAAa,GAAG,IAAI,CAAC7D,kBAAkB,CAAC0D,YAAY,EAAE,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEtB,EAAG;IACvEJ,IAAI,CAACb,QAAQ,GAAGD,QAAQ,CAACmG,QAAQ,CAAChD,UAAU;IAC5CrC,IAAI,CAACsC,MAAM,GAAGpD,QAAQ,CAACmG,QAAQ,CAAC9C,eAAe;IAC/CvC,IAAI,CAACmC,QAAQ,GAAG,IAAI,CAAC7D,aAAa;IAClC0B,IAAI,CAACwC,KAAK,GAAGtD,QAAQ,CAACsD,KAAK;IAC3BxC,IAAI,CAACyC,SAAS,GAAG,CAAC,GAAGvD,QAAQ,CAACU,EAAE,IAAInC,gBAAgB,CAAC0D,SAAS,EAAE,CAAC;IACjEnB,IAAI,CAAC0C,WAAW,GAAGxD,QAAQ,CAACwD,WAAW;IACvC1C,IAAI,CAAC2C,SAAS,GAAGzD,QAAQ,CAACyD,SAAS;IACnC3C,IAAI,CAAC4C,aAAa,GAAG1D,QAAQ,CAACmG,QAAQ,CAACzC,aAAa;IACpDhE,OAAO,CAACa,KAAK,CAACiH,oBAAoB,CAAC1G,IAAI,CAAC;EAC1C;EACQ2G,oBAAoBA,CAC1B3G,IAAwB,EACxBd,QAAoB,EACpBN,OAAmB;IAEnBoB,IAAI,CAACH,QAAQ,GAAGpC,gBAAgB,CAACgD,UAAU;IAC3CT,IAAI,CAAC6B,GAAG,GAAG3C,QAAQ,CAACU,EAAG;IACvBI,IAAI,CAACe,MAAM,GAAG7B,QAAQ,CAAC6B,MAAM;IAC7Bf,IAAI,CAACuG,kBAAkB,GAAGrH,QAAQ,CAACqH,kBAAkB;IACrDvG,IAAI,CAACb,QAAQ,GAAGD,QAAQ,CAACuG,IAAI;IAC7BzF,IAAI,CAACmC,QAAQ,GAAG,IAAI,CAAC7D,aAAa;IAClCM,OAAO,CAACa,KAAK,CAACiH,oBAAoB,CAAC1G,IAAI,CAAC;EAC1C;EAEA;;;;;;;;;EASA4G,sBAAsBA,CACpB5G,IAAkB,EAClB8F,OAA0B,EAC1B/G,QAAuB,EACvBC,MAAqB,EACrBJ,OAAmB;IAEnB,IAAI,CAACb,kBAAkB,CAAC8I,aAAa,CAACf,OAAO,CAAC,CAACtH,SAAS,CAAEsI,WAAW,IAAI;MACvE,IAAI,CAAC/I,kBAAkB,CAAC+G,OAAO,CAAC;QAC9B,GAAGgC,WAAW;QACd1E,SAAS,EAAE,CAAC0E,WAAW,CAACzB,QAAQ,CAAC;QACjCrD,WAAW,EAAEhC,IAAI,CAACgC,WAAW;QAC7BC,SAAS,EAAEjC,IAAI,CAACiC,SAAS;QACzBW,aAAa,EAAE5C,IAAI,CAAC4C;OACrB,CAAC;MACF,IAAI,CAACzE,eAAe,CAACwH,eAAe,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACO,oBAAoB,CAAClG,IAAI,EAAE8G,WAAW,EAAE/H,QAAQ,EAAEC,MAAM,EAAEJ,OAAO,CAAC;MACvE,MAAMmI,aAAa,GAAG;QACpBlF,GAAG,EAAE,GAAGiF,WAAW,CAAClH,EAAE,IAAInC,gBAAgB,CAAC0D,SAAS,EAAE;QACtDtB,QAAQ,EAAEpC,gBAAgB,CAAC0D,SAAS;QACpCJ,MAAM,EAAE+F,WAAW,CAAClH,EAAE;QACtBuC,QAAQ,EAAE,IAAI,CAAC7D;OAChB;MACDM,OAAO,CAACa,KAAK,CAAC0G,WAAW,CAACY,aAAa,CAAC;MACxCnI,OAAO,CAAC4H,8BAA8B,EAAE;IAC1C,CAAC,CAAC;EACJ;EACAQ,4BAA4BA,CAC1BhH,IAAwB,EACxB8F,OAAmB,EACnBlH,OAAmB;IAEnB,IAAI,CAACb,kBAAkB,CACpBkJ,gBAAgB,CAACnB,OAAO,CAAC,CACzBtH,SAAS,CAAEsI,WAAW,IAAI;MACzB,IAAI,CAAC/I,kBAAkB,CAAC6G,aAAa,CAACkC,WAAW,CAAC;MAClD,IAAI,CAACH,oBAAoB,CAAC3G,IAAI,EAAE8G,WAAW,EAAElI,OAAO,CAAC;IACvD,CAAC,CAAC;EACN;EAEAsI,uBAAuBA,CAACC,OAA2B;IACjD;IACA,IAAI,CAACpJ,kBAAkB,CAACqJ,gBAAgB,CAAC;MACvCxH,EAAE,EAAEuH,OAAO,CAACtF,GAAG;MACf4D,IAAI,EAAE0B,OAAO,CAAChI,QAAQ;MACtBoH,kBAAkB,EAAEY,OAAO,CAACZ,kBAAkB;MAC9CxF,MAAM,EAAE,CAACoG,OAAO,CAAC/G,EAAE,CAACiH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACjC,CAAC;EACJ;EACA;;;;;EAKAC,iBAAiBA,CAACpI,QAAsB,EAAEN,OAAmB;IAC3D,MAAM2I,WAAW,GAAG,IAAI,CAACxJ,kBAAkB,CAACwH,WAAW,CAAC,CAACrG,QAAQ,CAAC2C,GAAI,CAAC;IACvE,MAAM9C,QAAQ,GAAG,IAAI,CAACb,YAAY,CAACsJ,kBAAkB,CACnD5I,OAAO,EACPM,QAAQ,CAACiB,IAAI,CACd;IACD,MAAMnB,MAAM,GAAG,IAAI,CAACd,YAAY,CAACsJ,kBAAkB,CAAC5I,OAAO,EAAEM,QAAQ,CAACkB,EAAE,CAAC;IACzE,IAAImH,WAAW,IAAIxI,QAAQ,IAAIC,MAAM,EAAE;MACrC,MAAMyI,sBAAsB,GAAGF,WAAW,CAACnF,SAAS,CAAClC,IAAI,CACtDuF,IAAI,IAAKA,IAAI,CAAC3C,SAAS,KAAK,IAAI,CAACjE,cAAc,CAACe,EAAE,CACpD;MACD,IAAI6H,sBAAsB,EACxB,IAAI,CAACC,cAAc,CACjB;QACE,GAAGH,WAAW;QACdxF,sBAAsB,EAAE/C,MAAM,CAAC,iBAAiB,CAAC;QACjD8C,iBAAiB,EAAE/C,QAAQ,CAAC,iBAAiB;OAC9C,EACD0I,sBAAsB,EACtBvI,QAAQ,CAACC,QAAS,EAClBD,QAAQ,CAACoD,MAAO,CACjB;;EAEP;EAEA;;;;;;;;;EASQoF,cAAcA,CACpBH,WAA+B,EAC/BE,sBAAgC,EAChCtI,QAAgB,EAChBmD,MAAc;IAEd,MAAMqF,YAAY,GAAqB;MACrC/H,EAAE,EAAE2H,WAAW,CAAC3H,EAAE;MAClB2B,IAAI,EAAEgG,WAAW,CAAChG,IAAI;MACtBI,UAAU,EAAE4F,WAAW,CAAC5F,UAAU;MAClCa,KAAK,EAAE+E,WAAW,CAAC/E,KAAK;MACxBE,WAAW,EAAE6E,WAAW,CAAC7E,WAAW;MACpCC,SAAS,EAAE4E,WAAW,CAAC5E,SAAS;MAChCZ,sBAAsB,EAAEwF,WAAW,CAACxF,sBAAsB;MAC1DD,iBAAiB,EAAEyF,WAAW,CAACzF,iBAAiB;MAChDc,aAAa,EAAE6E,sBAAsB,EAAE7E,aAAa,IAAI,KAAK;MAC7DyC,QAAQ,EAAE;QACR,GAAGoC,sBAAsB;QACzBpF,UAAU,EAAElD,QAAQ;QACpBoD,eAAe,EAAED;;KAEpB;IACD,MAAMsF,qBAAqB,GAAGL,WAAW,CAACnF,SAAS,CAACyF,MAAM,CACvDpC,IAAI,IAAKA,IAAI,CAAC3C,SAAS,KAAK,IAAI,CAACjE,cAAc,CAACe,EAAE,CACpD;IACD,IAAI,CAAC7B,kBAAkB,CAACqH,UAAU,CAACuC,YAAY,CAAC,CAACnJ,SAAS,CAAEC,QAAQ,IAAI;MACtE,IAAI,CAACV,kBAAkB,CAAC2H,UAAU,CAAC;QACjC,GAAG6B,WAAW;QACdnF,SAAS,EAAE,CAAC,GAAGwF,qBAAqB,EAAEnJ,QAAQ,CAAC4G,QAAQ;OACxD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOAyC,cAAcA,CACZC,OAAyB,EACzBC,aAA4B,EAC5BC,SAAiB;IAEjB,MAAMhD,SAAS,GAAG,IAAI,CAAClH,kBAAkB,CAAC0D,YAAY,EAAE;IACxD,MAAMyG,mBAAmB,GAAmB,EAAE;IAC9C,IAAIH,OAAO,CAACvH,MAAM,GAAG,CAAC,EAAE;MACtB,IAAI,CAACzC,kBAAkB,CAACoK,QAAQ,EAAE,CAACC,OAAO,CAAEpI,IAAI,IAAI;QAClD,MAAMqI,SAAS,GAAGN,OAAO,CAAC7H,IAAI,CAC3BoI,GAAG,IACF,CAACA,GAAG,CAAC1I,EAAE,IAAII,IAAI,CAACgC,WAAW,IACzBsG,GAAG,CAAC3I,eAAe,IAAIK,IAAI,CAAC8B,iBAAiB,KAC/CwG,GAAG,CAACC,aAAa,IAAI,KAAK,CAC7B;QACD,MAAMC,OAAO,GAAGT,OAAO,CAAC7H,IAAI,CACzBoI,GAAG,IACF,CAACA,GAAG,CAAC1I,EAAE,IAAII,IAAI,EAAEiC,SAAS,IACxBqG,GAAG,CAAC3I,eAAe,IAAIK,IAAI,CAAC+B,sBAAsB,KACpDuG,GAAG,CAACC,aAAa,IAAI,KAAK,CAC7B;QACD,MAAME,QAAQ,GAAGxD,SAAS,CAACvD,GAAG,CAAC1B,IAAI,CAAC2B,UAAU,CAAC;QAC/C,MAAM+G,aAAa,GACjBV,aAAa,CAAC9H,IAAI,CACf2F,WAAW,IACVA,WAAW,CAAC8C,aAAa,IAAIN,SAAS,EAAEzI,EAAE,IAC1CiG,WAAW,CAAC+C,kBAAkB,IAAIJ,OAAO,EAAE5I,EAAE,IAC7CiG,WAAW,CAAC9E,MAAM,KAAKf,IAAI,CAACJ,EAAE,CACjC,IAAI,EAAE;QAET,IAAIyI,SAAS,IAAIG,OAAO,IAAIlI,MAAM,CAACC,IAAI,CAACmI,aAAa,CAAC,CAAClI,MAAM,IAAI,CAAC,EAAE;UAClE,MAAMiH,sBAAsB,GAAG,IAAI,CAACoB,mBAAmB,CACrD7I,IAAI,EACJiI,SAAS,CACV;UACD,MAAMa,YAAY,GAAiB;YACjC3I,IAAI,EAAE,GAAGkI,SAAS,EAAExG,GAAG,EAAEkH,QAAQ,EAAG,EAAE;YACtC3I,EAAE,EAAE,GAAGoI,OAAO,EAAE3G,GAAG,EAAEkH,QAAQ,EAAG,EAAE;YAClCxH,IAAI,EAAEvB,IAAI,CAACuB,IAAI;YACfC,eAAe,EAAEiH,QAAQ,EAAEtI,IAAK;YAChCyB,aAAa,EAAE6G,QAAQ,EAAErI,EAAG;YAC5ByB,GAAG,EAAE7B,IAAI,CAACJ,EAAG;YACbA,EAAE,EAAEI,IAAI,CAACJ,EAAG;YACZ+B,UAAU,EAAE3B,IAAI,CAAC2B,UAAU;YAC3BG,iBAAiB,EAAE9B,IAAI,CAAC8B,iBAAiB;YACzCC,sBAAsB,EAAE/B,IAAI,CAAC+B,sBAAsB;YACnDC,WAAW,EAAEqG,SAAS,EAAEzI,EAAG;YAC3BqC,SAAS,EAAEuG,OAAO,EAAE5I,EAAG;YACvBC,QAAQ,EAAEpC,gBAAgB,CAACyE,WAAW;YACtCC,QAAQ,EAAE,IAAI,CAAC7D,aAAa;YAC5Ba,QAAQ,EAAEsI,sBAAsB,EAAEpF,UAAU;YAC5CC,MAAM,EAAEmF,sBAAsB,EAAElF,eAAe;YAC/CC,KAAK,EAAExC,IAAI,CAACwC,KAAK;YACjBC,SAAS,EAAE,CAAC,GAAGzC,IAAI,CAACJ,EAAE,IAAInC,gBAAgB,CAAC0D,SAAS,EAAE,CAAC;YACvDuB,WAAW,EAAE1C,IAAI,CAAC0C,WAAW;YAC7BC,SAAS,EAAE3C,IAAI,CAAC2C,SAAS;YACzBC,aAAa,EACX5C,IAAI,CAACoC,SAAS,CAAClC,IAAI,CAAEuF,IAAI,IAAKA,IAAI,CAAC3C,SAAS,IAAImF,SAAS,CAAC,EACtDrF,aAAa,IAAI,KAAK,CAAE;WAC/B;UACDsF,mBAAmB,CAACc,IAAI,CAACF,YAAY,CAAC;;MAE1C,CAAC,CAAC;;IAEJ,OAAOZ,mBAAmB;EAC5B;EAEMe,uBAAuBA,CAC3BC,gBAAgC,EAChCnB,OAAyB;IAAA,IAAAoB,KAAA;IAAA,OAAAC,iBAAA;MAEzB,MAAMC,YAAY,GAAG,IAAIC,GAAG,EAAU;MACtC,MAAM1I,WAAW,GAAGuI,KAAI,CAACpL,kBAAkB,CAAC8C,cAAc,EAAE;MAC5D,MAAM0I,eAAe,GAAyB,EAAE;MAChD,KAAK,MAAMC,SAAS,IAAIzB,OAAO,EAAE;QAC/B,IAAI,CAACyB,SAAS,CAACjB,aAAa,EAAE;QAC9B,MAAMnC,UAAU,GAAGxF,WAAW,CAACV,IAAI,CAChCF,IAAI,IAAKA,IAAI,CAACuG,kBAAkB,KAAKiD,SAAS,CAAC7J,eAAe,CAChE;QACD,IAAIyG,UAAU,EAAE;UACd,MAAMqD,QAAQ,GAAGP,gBAAgB,CAAChJ,IAAI,CACnCF,IAAI,IAAKA,IAAI,CAACJ,EAAE,KAAKwG,UAAU,CAACrF,MAAM,CACxC;UACD,IAAI,CAAC0I,QAAQ,EAAE;UACf,IAAIJ,YAAY,CAACK,GAAG,CAACtD,UAAU,CAACrF,MAAM,CAAC,EAAE;UACzCsI,YAAY,CAACM,GAAG,CAACvD,UAAU,CAACrF,MAAM,CAAC;UACnCwI,eAAe,CAACP,IAAI,CAClBG,KAAI,CAACS,oBAAoB,CAACxD,UAAU,EAAEoD,SAAS,CAAC3H,GAAI,CAACkH,QAAQ,EAAE,CAAC,CACjE;;;MAGL,OAAOQ,eAAe;IAAC;EACzB;EACA;;;;;;;EAOQV,mBAAmBA,CACzB7I,IAAwB,EACxBiI,SAAiB;IAEjB,IAAIR,sBAAsB,GAAGzH,IAAI,CAACoC,SAAS,EAAElC,IAAI,CAC9CuF,IAAI,IAAKA,IAAI,CAAC3C,SAAS,KAAKmF,SAAS,CACvC;IACD,MAAM5C,QAAQ,GAAarF,IAAI,CAACoC,SAAS,GACrC;MACEU,SAAS,EAAEmF,SAAS;MACpBlH,MAAM,EAAEf,IAAI,CAACJ,EAAG;MAChByC,UAAU,EAAErC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,GACrCrC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEC,UAAU,GAC7BzE,iBAAiB;MACrB2E,eAAe,EAAEvC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEG,eAAe,GAC/CvC,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEG,eAAe,GAClC5E,sBAAsB;MAC1BiF,aAAa,EAAE5C,IAAI,CAACoC,SAAS,CAAC,CAAC,CAAC,EAAEQ,aAAa,IAAI;KACpD,GACD;MACEE,SAAS,EAAEmF,SAAS;MACpBlH,MAAM,EAAEf,IAAI,CAACJ,EAAG;MAChByC,UAAU,EACPrC,IAAgC,CAACb,QAAQ,IAAIvB,iBAAiB;MACjE2E,eAAe,EACZvC,IAAgC,CAACsC,MAAM,IAAI3E,sBAAsB;MACpEiF,aAAa,EAAE;KAChB;IACL,IAAI6E,sBAAsB,EAAE;MAC1B,OAAOA,sBAAsB;KAC9B,MAAM;MACL,MAAMoC,KAAK,GAAG7J,IAAI,CAACoC,SAAS,GAAGpC,IAAI,CAACoC,SAAS,GAAG,EAAE;MAClD,IAAI,CAACrE,kBAAkB,CACpB8E,cAAc,CAACwC,QAAQ,CAAC,CACxB7G,SAAS,CAAEuE,eAAe,IAAI;QAC7B0E,sBAAsB,GAAG1E,eAAe;QACxC,IAAI,CAAChF,kBAAkB,CAAC2H,UAAU,CAAC;UACjC,GAAG1F,IAAI;UACPoC,SAAS,EAAE,CAAC,GAAGyH,KAAK,EAAEpC,sBAAsB;SAC7C,CAAC;MACJ,CAAC,CAAC;MACJ,OAAOA,sBAAsB,IAAIpC,QAAQ;;EAE7C;EAEA;;;;;;EAMAyE,0BAA0BA,CAACC,SAAiB,EAAE3K,WAAuB;IACnE,IACEA,WAAW,CAACK,KAAK,CAACuK,aAAa,CAACnC,MAAM,CACnC3E,IAAI,IAAKA,IAAI,CAAC,iBAAiB,CAAC,IAAI6G,SAAS,CAC/C,CAACvJ,MAAM,IAAI,CAAC,IACb,IAAI,CAACzC,kBAAkB,CACpBoK,QAAQ,EAAE,CACVjI,IAAI,CACFF,IAAI,IACHA,IAAI,CAAC8B,iBAAiB,IAAIiI,SAAS,IACnC/J,IAAI,CAAC+B,sBAAsB,IAAIgI,SAAS,CAC3C,EACH;MACA,MAAME,KAAK,GAAG,IAAI,CAACC,gBAAgB,CAACH,SAAS,CAAC;MAC9CE,KAAK,CAAC7B,OAAO,CAAEpI,IAAI,IAAI;QACrB,IAAI,CAACmK,kBAAkB,CAACnK,IAAI,EAAEZ,WAAW,CAAC;MAC5C,CAAC,CAAC;;IAEJ,IAAI,CAACkH,+BAA+B,CAACyD,SAAS,EAAE3K,WAAW,CAAC;EAC9D;EAEAkH,+BAA+BA,CAACyD,SAAiB,EAAE3K,WAAuB;IACxE;IACA,IACEA,WAAW,CAACK,KAAK,CAACuK,aAAa,CAACnC,MAAM,CACnC3E,IAAI,IACHA,IAAI,CAAC,iBAAiB,CAAC,IAAI6G,SAAS,IACpC7G,IAAI,CAAC,UAAU,CAAC,IAAIzF,gBAAgB,CAACqC,gBAAgB,CACxD,CAACU,MAAM,IAAI,CAAC,IACb,IAAI,CAACzC,kBAAkB,CACpB8C,cAAc,EAAE,CAChBX,IAAI,CAAEF,IAAI,IAAKA,IAAI,CAACuG,kBAAkB,IAAIwD,SAAS,CAAC,EACvD;MACA,MAAM3D,UAAU,GAAG,IAAI,CAACrI,kBAAkB,CACvC8C,cAAc,EAAE,CAChBX,IAAI,CAAEF,IAAI,IAAKA,IAAI,CAACuG,kBAAkB,IAAIwD,SAAS,CAAC;MACvD,MAAMK,mBAAmB,GACvBhL,WAAW,CAACK,KACb,CAACH,aAAa,CAACS,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC,QAAQ,CAAC,IAAIoG,UAAU,EAAErF,MAAM,CAAC;MACpE,IAAIqF,UAAU,IAAI,CAACgE,mBAAmB,EACpC,IAAI,CAACC,mBAAmB,CAACjE,UAAU,EAAEhH,WAAW,CAAC;;EAEvD;EAEA;;;;;;EAMA8K,gBAAgBA,CAACH,SAAiB;IAChC,OAAO,IAAI,CAAChM,kBAAkB,CAC3BoK,QAAQ,EAAE,CACVN,MAAM,CACJ3I,QAAQ,IACPA,QAAQ,CAAC6C,sBAAsB,KAAKgI,SAAS,IAC7C7K,QAAQ,CAAC4C,iBAAiB,KAAKiI,SAAS,CAC3C;EACL;EAEA;;;;;;;EAOQI,kBAAkBA,CACxBnK,IAAwB,EACxBsK,WAAuB;IAEvB,MAAMjJ,OAAO,GAAG,IAAI,CAACnD,YAAY,CAACqM,yBAAyB,CACzDD,WAAW,EACXtK,IAAI,CAAC8B,iBAAiB,CACvB;IACD,MAAMR,QAAQ,GAAG,IAAI,CAACpD,YAAY,CAACqM,yBAAyB,CAC1DD,WAAW,EACXtK,IAAI,CAAC+B,sBAAsB,CAC5B;IACD,IAAIV,OAAO,IAAIC,QAAQ,EAAE;MACvB,MAAMpC,QAAQ,GAAG,IAAI,CAACkC,WAAW,CAACpB,IAAI,EAAEqB,OAAO,EAAEC,QAAQ,CAAC;MAC1DgJ,WAAW,CAAC7K,KAAK,CAAC0G,WAAW,CAAC;QAC5BtE,GAAG,EAAE,GAAG3C,QAAQ,CAACU,EAAE,IAAInC,gBAAgB,CAAC0D,SAAS,EAAE;QACnDtB,QAAQ,EAAEpC,gBAAgB,CAAC0D,SAAS;QACpCJ,MAAM,EAAE7B,QAAQ,CAACU,EAAE;QACnBuC,QAAQ,EAAEjD,QAAQ,CAACiD;OACpB,CAAC;MACF,MAAMqI,cAAc,GAAG,IAAI,CAACzM,kBAAkB,CAC3C8C,cAAc,EAAE,CAChBX,IAAI,CAAEF,IAAI,IAAKA,IAAI,CAACe,MAAM,IAAI7B,QAAQ,CAACU,EAAE,CAAC;MAC7C,IAAI4K,cAAc,EAAE,IAAI,CAACH,mBAAmB,CAACG,cAAc,EAAEF,WAAW,CAAC;MACzEA,WAAW,CAAC9D,8BAA8B,EAAE;MAC3C8D,WAAW,CAAC7K,KAA4B,CAACgL,WAAW,CAACvL,QAAQ,CAAC;;EAEnE;EACQmL,mBAAmBA,CAACrK,IAAgB,EAAEsK,WAAuB;IACnE,MAAMjJ,OAAO,GAAG,IAAI,CAACnD,YAAY,CAACqM,yBAAyB,CACzDD,WAAW,EACXtK,IAAI,CAACuG,kBAAkB,CACxB;IACD,IAAIlF,OAAO,EAAE;MACX,MAAMnC,QAAQ,GAAuB;QACnCiB,IAAI,EAAEkB,OAAO,CAAC,KAAK,CAAC;QACpBQ,GAAG,EAAE7B,IAAI,CAACJ,EAAG;QACbmB,MAAM,EAAEf,IAAI,CAACe,MAAM;QACnBX,EAAE,EAAE,GAAGJ,IAAI,CAACe,MAAM,IAAItD,gBAAgB,CAAC0D,SAAS,EAAE;QAClDoF,kBAAkB,EAAEvG,IAAI,CAACuG,kBAAkB;QAC3C1G,QAAQ,EAAEpC,gBAAgB,CAACgD,UAAU;QACrC0B,QAAQ,EAAE,IAAI,CAAC7D,aAAa;QAC5Ba,QAAQ,EAAEa,IAAI,CAACyF;OAChB;MACA6E,WAAW,CAAC7K,KAA4B,CAACgL,WAAW,CAACvL,QAAQ,CAAC;;EAEnE;EAEA;;;;;EAKAwL,kBAAkBA,CAACX,SAAiB;IAClC,MAAMY,YAAY,GAAG,IAAI,CAAC5M,kBAAkB,CACzCoK,QAAQ,EAAE,CACVN,MAAM,CACJ7H,IAAI,IACHA,IAAI,CAAC+B,sBAAsB,KAAKgI,SAAS,IACzC/J,IAAI,CAAC8B,iBAAiB,KAAKiI,SAAS,CACvC;IACH,IAAI,CAAChM,kBAAkB,CAAC6M,WAAW,CAACD,YAAY,CAAC;EACnD;EAEA;;;;;EAKA3G,wBAAwBA,CAAC9E,QAAsB;IAC7C,IAAI,CAACnB,kBAAkB,CAAC2G,UAAU,CAAC,CAACxF,QAAQ,CAAC2C,GAAI,CAAC;IAClD,IAAI,CAAC9D,kBAAkB,CAAC4G,MAAM,CAAC,CAACzF,QAAQ,CAAC2C,GAAI,CAAC;IAC9C,IAAI,CAAC3D,YAAY,CAACuI,iBAAiB,CAAC,CAACvH,QAAQ,CAAC2C,GAAI,CAAC;IACnD,IAAI,CAAC9D,kBAAkB,CAACyG,0BAA0B,CAChD,QAAQ,EACR,CAACtF,QAAQ,CAAC2C,GAAI,CACf;EACH;EAEA;;;;;;;EAOAkC,4BAA4BA,CAC1B7E,QAAsB,EACtBN,OAAmB,EACnBiM,WAAoB;IAEpB,IAAI,CAACA,WAAW,EACd,IAAI,CAAC9M,kBAAkB,CAAC+M,wBAAwB,CAAC;MAC/ChI,SAAS,EAAE,IAAI,CAACjE,cAAc,CAACe,EAAG;MAClC+I,aAAa,EAAEzJ,QAAQ,CAAC8C,WAAW;MACnC4G,kBAAkB,EAAE1J,QAAQ,CAAC+C,SAAS;MACtClB,MAAM,EAAE,CAAC7B,QAAQ,CAAC2C;KACnB,CAAC;IACJ,MAAM0F,WAAW,GAAG,IAAI,CAACxJ,kBAAkB,CAACwH,WAAW,CAAC,CAACrG,QAAQ,CAAC2C,GAAI,CAAC;IACvE,MAAM4F,sBAAsB,GAAGF,WAAW,CAACnF,SAAS,CAAClC,IAAI,CACtDuF,IAAI,IAAKA,IAAI,CAAC3C,SAAS,KAAK,IAAI,CAACjE,cAAc,CAACe,EAAE,CACpD;IACD,IAAI6H,sBAAsB,EAAE;MAC1B,IAAI,CAACoD,WAAW,EACd,IAAI,CAAC9M,kBAAkB,CAACgN,cAAc,CAACtD,sBAAsB,CAAC7H,EAAG,CAAC;MACpE,IAAI,CAAC7B,kBAAkB,CAACiN,sBAAsB,CAC5CzD,WAAW,CAAC3H,EAAG,EACf6H,sBAAsB,CAAC7H,EAAG,CAC3B;;IAEFhB,OAAO,CAACa,KAAyB,CAACqE,cAAc,CAAC5E,QAAQ,CAAC;EAC7D;EAEA+L,sBAAsBA,CAACC,WAAyB,EAAEtM,OAAmB;IACnE,IAAIoB,IAAI,GAAyB,IAAI;IACpCpB,OAAO,CAACa,KAAyB,CAACH,aAAa,CAAC8I,OAAO,CAAElJ,QAAQ,IAAI;MACpE,IACEA,QAAQ,CAAC,KAAK,CAAC,KAAKgM,WAAW,CAACrJ,GAAG,IACnC3C,QAAQ,CAAC,UAAU,CAAC,KAAKgM,WAAW,CAACrL,QAAQ,EAC7C;QACAG,IAAI,GAAGd,QAAQ;;IAEnB,CAAC,CAAC;IACF,IAAI,CAACc,IAAI,EAAE;IAEX,MAAMd,QAAQ,GAAGc,IAAI;IACrB,IAAI,CAAC5B,iBAAiB,CAAC+M,iBAAiB,CAACvM,OAAO,CAACa,KAAK,EAAEP,QAAQ,EAAE;MAChEqC,IAAI,EAAE2J,WAAW,CAAC3J,IAAI;MACtBmB,WAAW,EAAEwI,WAAW,CAACxI,WAAW;MACpCC,SAAS,EAAEuI,WAAW,CAACvI,SAAS;MAChCH,KAAK,EAAE0I,WAAW,CAAC1I;KACpB,CAAC;IACF,MAAM8C,YAAY,GAAG,IAAI,CAACvH,kBAAkB,CAACwH,WAAW,CAAC2F,WAAW,CAACtL,EAAG,CAAC;IACzE0F,YAAY,CAAC/D,IAAI,GAAG2J,WAAW,CAAC3J,IAAI;IACpC+D,YAAY,CAAC5C,WAAW,GAAGwI,WAAW,CAACxI,WAAW;IAClD4C,YAAY,CAAC3C,SAAS,GAAGuI,WAAW,CAACvI,SAAS;IAC9C2C,YAAY,CAAC9C,KAAK,GAAG0I,WAAW,CAAC1I,KAAK;IACtC,IAAI,CAACzE,kBAAkB,CAAC2H,UAAU,CAACJ,YAAY,CAAC;EAClD;EAEAsE,oBAAoBA,CAAC5J,IAAgB,EAAEoL,YAAoB;IACzD,MAAMZ,cAAc,GAAuB;MACzCrK,IAAI,EAAEiL,YAAY;MAClBhL,EAAE,EAAE,GAAGJ,IAAI,CAACe,MAAM,IAAItD,gBAAgB,CAAC0D,SAAS,EAAE;MAClDU,GAAG,EAAE7B,IAAI,CAACJ,EAAG;MACbmB,MAAM,EAAEf,IAAI,CAACe,MAAM;MACnBwF,kBAAkB,EAAEvG,IAAI,CAACuG,kBAAkB;MAC3C1G,QAAQ,EAAEpC,gBAAgB,CAACgD,UAAU;MACrC0B,QAAQ,EAAE,IAAI,CAAC7D,aAAa;MAC5Ba,QAAQ,EAAEa,IAAI,CAACyF;KAChB;IACD,OAAO+E,cAAc;EACvB;EAAC,QAAAa,CAAA,G;qBApgCUxN,sBAAsB,EAAAyN,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,SAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,iBAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAtBzO,sBAAsB;IAAA0O,OAAA,EAAtB1O,sBAAsB,CAAA2O,IAAA;IAAAC,UAAA,EAFrB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}