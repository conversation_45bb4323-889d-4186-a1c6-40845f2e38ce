{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../cardinality/cardinality.service\";\nexport class GojsCommonService {\n  constructor(cardinalityService) {\n    this.cardinalityService = cardinalityService;\n    this._gojsDiagramSubject = new BehaviorSubject(null);\n  }\n  /**\n   * Sets the GoJS diagram instance.\n   *\n   * This method emits the specified `go.Diagram` instance to the `_gojsDiagramSubject`,\n   * allowing it to be shared or accessed throughout the application.\n   *\n   * @param diagram - The `go.Diagram` instance to be set as the current diagram.\n   */\n  setGojsDiagram(diagram) {\n    this._gojsDiagramSubject.next(diagram);\n  }\n  /**\n   * Subscribes to changes in the GoJS diagram instance.\n   *\n   * This method provides an observable stream of `go.Diagram` values, allowing\n   * consumers to react to updates in the current GoJS diagram instance.\n   *\n   * @returns An `Observable` that emits the current `go.Diagram` instance, or `null` if none is set.\n   */\n  gojsDiagramChanges() {\n    return this._gojsDiagramSubject.asObservable();\n  }\n  /**\n   * Updates the opacity of an RGBA color.\n   *\n   * @private\n   * @param rgbaColor The original RGBA color string.\n   * @param newOpacity The desired opacity level as a number between 0 and 1.\n   * @returns {string} A new RGBA color string with the updated opacity.\n   *\n   * @memberOf DiagramEditorComponent\n   */\n  updateRGBAColorWithOpacity(rgbaColor, newOpacity) {\n    // Extract RGBA components\n    const rgbaValues = rgbaColor.match(/\\d+(\\.\\d+)?/g);\n    if (rgbaValues) {\n      const red = parseFloat(rgbaValues[0]);\n      const green = parseFloat(rgbaValues[1]);\n      const blue = parseFloat(rgbaValues[2]);\n      // Adjust the alpha value (opacity)\n      const adjustedAlpha = newOpacity;\n      // Update the RGBA color with the new opacity\n      const newRgbaColor = `rgba(${red}, ${green}, ${blue}, ${adjustedAlpha})`;\n      return newRgbaColor;\n    } else {\n      // Handle the case where rgbaValues is null\n      return rgbaColor; // Return the original color as fallback\n    }\n  }\n  isGojsDiagramClassNode(node) {\n    return node && node.category === GojsNodeCategory.Class || node.category === GojsNodeCategory.AssociativeClass && typeof node.idTemplateClass === 'number';\n  }\n  isGojsDiagramEnumerationNode(node) {\n    return node && node.category === GojsNodeCategory.Enumeration && typeof node.idTemplateEnumeration === 'number';\n  }\n  isGojsDiagramAttributeNode(node) {\n    return node && (node.category === GojsNodeCategory.Attribute || node.category === GojsNodeCategory.Operation);\n  }\n  isGojsDiagramLiteralNode(node) {\n    return node && node.category === GojsNodeCategory.EnumerationLiteral;\n  }\n  isGojsPaletteFolderNode(node) {\n    return node && node.category === GojsNodeCategory.Folder;\n  }\n  isGojsLinkNode(node) {\n    return node && node.category === GojsNodeCategory.Association;\n  }\n  /**\n   * Updates properties of items within node data objects in the GoJS model's nodeDataArray based on a condition.\n   *\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\n   * @param {UpdateCondition} condition - A function to check if an item meets the update criteria.\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n   */\n  updateNodeDataItemsProperties(model, condition, properties) {\n    model.nodeDataArray.forEach(nodeData => {\n      if (nodeData && nodeData['items']) {\n        nodeData['items'].forEach(item => {\n          if (condition(item)) {\n            for (const key in properties) {\n              if (properties.hasOwnProperty(key)) {\n                model.commit(model => {\n                  model.set(item, key, properties[key]);\n                }, null);\n              }\n            }\n          }\n        });\n      }\n    });\n  }\n  /**\n   * Updates properties of node data objects in the GoJS model's nodeDataArray based on a condition.\n   *\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\n   * @param {NodeUpdateCondition} condition - A function to check if a node data object meets the update criteria.\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n   */\n  updateNodeDataProperties(model, condition, properties) {\n    model.nodeDataArray.forEach(nodeData => {\n      if (condition(nodeData)) {\n        for (const key in properties) {\n          if (properties.hasOwnProperty(key)) {\n            model.commit(model => {\n              model.set(nodeData, key, properties[key]);\n            }, null);\n          }\n        }\n      }\n    });\n  }\n  /**\n   * Sets multiple properties on a GoJS model data object.\n   *\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`, `go.GraphObject`).\n   * @param {go.ObjectData} data - The data object (node or link data) to update.\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n   */\n  setDataProperties(model, data, properties) {\n    Object.keys(properties).forEach(key => {\n      model.commit(m => {\n        m.set(data, key, properties[key]);\n      }, null);\n    });\n  }\n  /**\n   *Commit transaction For updating the diagram node data\n      * @param {*} propertyData\n   * @memberof DiagramEditorComponent\n   */\n  commitGroupNodeData(nodeData, properties, gojsDiagram) {\n    nodeData.forEach(node => {\n      this.setDataProperties(gojsDiagram.model, node, properties);\n    });\n  }\n  /**\n   * For removing the group node like class and enumeration node with children from palette and diagram\n   * @param {GojsNodeCategory} category - The Category of node\n   * @param {go.Diagram} goJsDiagram - Diagram\n   * @memberof GojsCommonService\n   */\n  removeGroupNodeWithItems(idTemplate, category, goJsDiagram) {\n    const diagramNodes = goJsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] === idTemplate && node['category'] === category || node['idTemplateEnumeration'] === idTemplate && node['category'] === category);\n    goJsDiagram.model.removeNodeDataCollection(diagramNodes);\n  }\n  removeNodeFromDiagram(goJsDiagram, condition) {\n    const model = goJsDiagram.model;\n    model.nodeDataArray.forEach(nodeData => {\n      if (condition(nodeData)) {\n        model.removeNodeData(nodeData);\n      }\n    });\n  }\n  removeAssociationLink(diagram, classData) {\n    const relatedLinkData = diagram.model.linkDataArray.filter(link => link['idSourceTempClass'] == classData.idTemplateClass || link['idDestinationTempClass'] == classData.idTemplateClass);\n    if (relatedLinkData) {\n      relatedLinkData.forEach(associationLink => {\n        this.removeLinkToLink(diagram, link => link.to === associationLink['labelKeys'][0] && link.category === GojsNodeCategory.LinkToLink);\n      });\n      diagram.model.removeLinkDataCollection(relatedLinkData);\n    }\n  }\n  removeLinkToLink(diagram, condition, idAssociativeClass) {\n    const model = diagram.model;\n    const linksToRemove = model.linkDataArray.find(condition);\n    if (linksToRemove) {\n      model.removeLinkData(linksToRemove);\n      this.cardinalityService.removeLinkToLink(linksToRemove['key']);\n    } else if (idAssociativeClass) {\n      this.cardinalityService.removeLinkToLinkByProperty('idAssociativeClass', idAssociativeClass);\n    }\n  }\n  checkGroupNodeExist(gojsDiagram, nodeData) {\n    const existingNode = gojsDiagram.model.nodeDataArray.find(node => {\n      return node['treeNodeTag'] === nodeData.tag;\n    });\n    if (existingNode) {\n      // Select the node using the diagram's selection manager\n      const part = gojsDiagram.findPartForData(existingNode);\n      if (part) {\n        // gojsDiagram.clearSelection();\n        gojsDiagram.select(part); // select the part\n      }\n      return true;\n    } else if (nodeData.category === GojsNodeCategory.AssociativeClass) {\n      // If one link to link is already present, select it and restrict to drop another associative class which have link to link with the same link\n      const linkToLink = this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == nodeData.data.idTemplateClass);\n      if (linkToLink) {\n        const existingLinkToLink = gojsDiagram.model.linkDataArray.find(node => {\n          return node['category'] === GojsNodeCategory.LinkToLink && node['idLink'] === linkToLink.idLink;\n        });\n        if (existingLinkToLink) {\n          // Select the node using the diagram's selection manager\n          const part = gojsDiagram.findPartForData(existingLinkToLink);\n          if (part) {\n            gojsDiagram.clearSelection();\n            gojsDiagram.select(part); // select the part\n          }\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  selectMultipleGroupNodeExist(goJsDiagram, nodeData) {\n    const existingParts = [];\n    nodeData.forEach(data => {\n      const matchingNode = goJsDiagram.model.nodeDataArray.find(node => node['treeNodeTag'] === data.tag);\n      if (matchingNode) {\n        const part = goJsDiagram.findPartForData(matchingNode);\n        if (part) {\n          existingParts.push(part);\n        }\n      }\n    });\n    if (existingParts.length > 0) {\n      goJsDiagram.clearSelection(); // Clear any previous selection\n      goJsDiagram.selectCollection(existingParts); // Select all matched parts\n      goJsDiagram.commandHandler.scrollToPart(existingParts[0]); // Optional: bring first selected node into view\n    }\n  }\n  static #_ = this.ɵfac = function GojsCommonService_Factory(t) {\n    return new (t || GojsCommonService)(i0.ɵɵinject(i1.CardinalityService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsCommonService,\n    factory: GojsCommonService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "GojsNodeCategory", "GojsCommonService", "constructor", "cardinalityService", "_gojsDiagramSubject", "setGojsDiagram", "diagram", "next", "gojsDiagramChanges", "asObservable", "updateRGBAColorWithOpacity", "rgbaColor", "newOpacity", "rgbaValues", "match", "red", "parseFloat", "green", "blue", "adjustedAlpha", "newRgbaColor", "isGojsDiagramClassNode", "node", "category", "Class", "AssociativeClass", "idTemplateClass", "isGojsDiagramEnumerationNode", "Enumeration", "idTemplateEnumeration", "isGojsDiagramAttributeNode", "Attribute", "Operation", "isGojsDiagramLiteralNode", "EnumerationLiteral", "isGojsPaletteFolderNode", "Folder", "isGojsLinkNode", "Association", "updateNodeDataItemsProperties", "model", "condition", "properties", "nodeDataArray", "for<PERSON>ach", "nodeData", "item", "key", "hasOwnProperty", "commit", "set", "updateNodeDataProperties", "setDataProperties", "data", "Object", "keys", "m", "commitGroupNodeData", "gojsDiagram", "removeGroupNodeWithItems", "idTemplate", "goJsDiagram", "diagramNodes", "filter", "removeNodeDataCollection", "removeNodeFromDiagram", "removeNodeData", "removeAssociationLink", "classData", "relatedLinkData", "linkDataArray", "link", "associationLink", "removeLinkToLink", "to", "LinkToLink", "removeLinkDataCollection", "idAssociativeClass", "linksToRemove", "find", "removeLinkData", "removeLinkToLinkByProperty", "checkGroupNodeExist", "existingNode", "tag", "part", "findPartForData", "select", "linkToLink", "getLinkToLinks", "existingLinkToLink", "idLink", "clearSelection", "selectMultipleGroupNodeExist", "existingParts", "matchingNode", "push", "length", "selectCollection", "command<PERSON><PERSON>ler", "scrollToPart", "_", "i0", "ɵɵinject", "i1", "CardinalityService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitCodeBassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojsCommon\\gojs-common.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport go from 'gojs';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n  NodeUpdateCondition,\r\n  UpdateCondition,\r\n  UpdateProperties,\r\n} from 'src/app/shared/model/gojs';\r\nimport { TreeNode } from 'src/app/shared/model/treeNode';\r\nimport { CardinalityService } from '../../cardinality/cardinality.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsCommonService {\r\n  private _gojsDiagramSubject = new BehaviorSubject<go.Diagram | null>(null);\r\n  constructor(private cardinalityService: CardinalityService) {}\r\n\r\n  /**\r\n   * Sets the GoJS diagram instance.\r\n   *\r\n   * This method emits the specified `go.Diagram` instance to the `_gojsDiagramSubject`,\r\n   * allowing it to be shared or accessed throughout the application.\r\n   *\r\n   * @param diagram - The `go.Diagram` instance to be set as the current diagram.\r\n   */\r\n  setGojsDiagram(diagram: go.Diagram | null): void {\r\n    this._gojsDiagramSubject.next(diagram);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to changes in the GoJS diagram instance.\r\n   *\r\n   * This method provides an observable stream of `go.Diagram` values, allowing\r\n   * consumers to react to updates in the current GoJS diagram instance.\r\n   *\r\n   * @returns An `Observable` that emits the current `go.Diagram` instance, or `null` if none is set.\r\n   */\r\n  gojsDiagramChanges(): Observable<go.Diagram | null> {\r\n    return this._gojsDiagramSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Updates the opacity of an RGBA color.\r\n   *\r\n   * @private\r\n   * @param rgbaColor The original RGBA color string.\r\n   * @param newOpacity The desired opacity level as a number between 0 and 1.\r\n   * @returns {string} A new RGBA color string with the updated opacity.\r\n   *\r\n   * @memberOf DiagramEditorComponent\r\n   */\r\n  updateRGBAColorWithOpacity(rgbaColor: string, newOpacity: number): string {\r\n    // Extract RGBA components\r\n    const rgbaValues = rgbaColor.match(/\\d+(\\.\\d+)?/g);\r\n    if (rgbaValues) {\r\n      const red = parseFloat(rgbaValues[0]);\r\n      const green = parseFloat(rgbaValues[1]);\r\n      const blue = parseFloat(rgbaValues[2]);\r\n\r\n      // Adjust the alpha value (opacity)\r\n      const adjustedAlpha = newOpacity;\r\n\r\n      // Update the RGBA color with the new opacity\r\n      const newRgbaColor = `rgba(${red}, ${green}, ${blue}, ${adjustedAlpha})`;\r\n\r\n      return newRgbaColor;\r\n    } else {\r\n      // Handle the case where rgbaValues is null\r\n      return rgbaColor; // Return the original color as fallback\r\n    }\r\n  }\r\n\r\n  isGojsDiagramClassNode(node: any): node is GojsDiagramClassNode {\r\n    return (\r\n      (node && node.category === GojsNodeCategory.Class) ||\r\n      (node.category === GojsNodeCategory.AssociativeClass &&\r\n        typeof node.idTemplateClass === 'number')\r\n    );\r\n  }\r\n  isGojsDiagramEnumerationNode(node: any): node is GojsDiagramEnumerationNode {\r\n    return (\r\n      node &&\r\n      node.category === GojsNodeCategory.Enumeration &&\r\n      typeof node.idTemplateEnumeration === 'number'\r\n    );\r\n  }\r\n\r\n  isGojsDiagramAttributeNode(node: any): node is GojsDiagramAttributeNode {\r\n    return (\r\n      node &&\r\n      (node.category === GojsNodeCategory.Attribute ||\r\n        node.category === GojsNodeCategory.Operation)\r\n    );\r\n  }\r\n  isGojsDiagramLiteralNode(node: any): node is GojsDiagramLiteralNode {\r\n    return node && node.category === GojsNodeCategory.EnumerationLiteral;\r\n  }\r\n  isGojsPaletteFolderNode(node: any): node is GojsFolderNode {\r\n    return node && node.category === GojsNodeCategory.Folder;\r\n  }\r\n\r\n  isGojsLinkNode(node: any): node is GojsLinkNode {\r\n    return node && node.category === GojsNodeCategory.Association;\r\n  }\r\n\r\n  /**\r\n   * Updates properties of items within node data objects in the GoJS model's nodeDataArray based on a condition.\r\n   *\r\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\r\n   * @param {UpdateCondition} condition - A function to check if an item meets the update criteria.\r\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\r\n   */\r\n  updateNodeDataItemsProperties(\r\n    model: go.Model,\r\n    condition: UpdateCondition,\r\n    properties: UpdateProperties\r\n  ): void {\r\n    model.nodeDataArray.forEach((nodeData) => {\r\n      if (nodeData && nodeData['items']) {\r\n        nodeData['items'].forEach((item: any) => {\r\n          if (condition(item)) {\r\n            for (const key in properties) {\r\n              if (properties.hasOwnProperty(key)) {\r\n                model.commit((model) => {\r\n                  model.set(item, key, properties[key]);\r\n                }, null);\r\n              }\r\n            }\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Updates properties of node data objects in the GoJS model's nodeDataArray based on a condition.\r\n   *\r\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\r\n   * @param {NodeUpdateCondition} condition - A function to check if a node data object meets the update criteria.\r\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\r\n   */\r\n  updateNodeDataProperties(\r\n    model: go.Model,\r\n    condition: NodeUpdateCondition,\r\n    properties: UpdateProperties\r\n  ): void {\r\n    model.nodeDataArray.forEach((nodeData) => {\r\n      if (condition(nodeData)) {\r\n        for (const key in properties) {\r\n          if (properties.hasOwnProperty(key)) {\r\n            model.commit((model) => {\r\n              model.set(nodeData, key, properties[key]);\r\n            }, null);\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Sets multiple properties on a GoJS model data object.\r\n   *\r\n   * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`, `go.GraphObject`).\r\n   * @param {go.ObjectData} data - The data object (node or link data) to update.\r\n   * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\r\n   */\r\n  setDataProperties(\r\n    model: go.Model,\r\n    data: go.ObjectData,\r\n    properties: UpdateProperties\r\n  ): void {\r\n    Object.keys(properties).forEach((key) => {\r\n      model.commit((m) => {\r\n        m.set(data, key, properties[key]);\r\n      }, null);\r\n    });\r\n  }\r\n\r\n  /**\r\n   *Commit transaction For updating the diagram node data\r\n\r\n   * @param {*} propertyData\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  commitGroupNodeData(\r\n    nodeData: go.ObjectData[],\r\n    properties: UpdateProperties,\r\n    gojsDiagram: go.Diagram\r\n  ): void {\r\n    nodeData.forEach((node) => {\r\n      this.setDataProperties(gojsDiagram.model, node, properties);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * For removing the group node like class and enumeration node with children from palette and diagram\r\n   * @param {GojsNodeCategory} category - The Category of node\r\n   * @param {go.Diagram} goJsDiagram - Diagram\r\n   * @memberof GojsCommonService\r\n   */\r\n  removeGroupNodeWithItems(\r\n    idTemplate: number,\r\n    category: GojsNodeCategory,\r\n    goJsDiagram: go.Diagram\r\n  ) {\r\n    const diagramNodes = goJsDiagram.model.nodeDataArray.filter(\r\n      (node) =>\r\n        (node['idTemplateClass'] === idTemplate &&\r\n          node['category'] === category) ||\r\n        (node['idTemplateEnumeration'] === idTemplate &&\r\n          node['category'] === category)\r\n    );\r\n    goJsDiagram.model.removeNodeDataCollection(diagramNodes);\r\n  }\r\n\r\n  removeNodeFromDiagram(\r\n    goJsDiagram: go.Diagram,\r\n    condition: NodeUpdateCondition\r\n  ): void {\r\n    const model = goJsDiagram.model as go.GraphLinksModel;\r\n    model.nodeDataArray.forEach((nodeData) => {\r\n      if (condition(nodeData)) {\r\n        model.removeNodeData(nodeData);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeAssociationLink(diagram: go.Diagram, classData: GojsDiagramClassNode) {\r\n    const relatedLinkData = (\r\n      diagram.model as go.GraphLinksModel\r\n    ).linkDataArray.filter(\r\n      (link) =>\r\n        link['idSourceTempClass'] == classData.idTemplateClass ||\r\n        link['idDestinationTempClass'] == classData.idTemplateClass\r\n    );\r\n\r\n    if (relatedLinkData) {\r\n      relatedLinkData.forEach((associationLink) => {\r\n        this.removeLinkToLink(\r\n          diagram,\r\n          (link) =>\r\n            link.to === associationLink['labelKeys'][0] &&\r\n            link.category === GojsNodeCategory.LinkToLink\r\n        );\r\n      });\r\n      (diagram.model as go.GraphLinksModel).removeLinkDataCollection(\r\n        relatedLinkData\r\n      );\r\n    }\r\n  }\r\n\r\n  removeLinkToLink(\r\n    diagram: go.Diagram,\r\n    condition: (link: any) => boolean,\r\n    idAssociativeClass?: number\r\n  ): void {\r\n    const model = diagram.model as go.GraphLinksModel;\r\n    const linksToRemove = model.linkDataArray.find(condition);\r\n    if (linksToRemove) {\r\n      model.removeLinkData(linksToRemove);\r\n      this.cardinalityService.removeLinkToLink(linksToRemove['key']);\r\n    } else if (idAssociativeClass) {\r\n      this.cardinalityService.removeLinkToLinkByProperty(\r\n        'idAssociativeClass',\r\n        idAssociativeClass\r\n      );\r\n    }\r\n  }\r\n\r\n  checkGroupNodeExist(gojsDiagram: go.Diagram, nodeData: TreeNode): boolean {\r\n    const existingNode = gojsDiagram.model.nodeDataArray.find((node) => {\r\n      return node['treeNodeTag'] === nodeData.tag;\r\n    });\r\n    if (existingNode) {\r\n      // Select the node using the diagram's selection manager\r\n      const part = gojsDiagram.findPartForData(existingNode);\r\n      if (part) {\r\n        // gojsDiagram.clearSelection();\r\n        gojsDiagram.select(part); // select the part\r\n      }\r\n\r\n      return true;\r\n    } else if (nodeData.category === GojsNodeCategory.AssociativeClass) {\r\n      // If one link to link is already present, select it and restrict to drop another associative class which have link to link with the same link\r\n      const linkToLink = this.cardinalityService\r\n        .getLinkToLinks()\r\n        .find(\r\n          (link) =>\r\n            link.idAssociativeClass ==\r\n            (nodeData.data as GojsDiagramClassNode).idTemplateClass\r\n        );\r\n      if (linkToLink) {\r\n        const existingLinkToLink = (\r\n          gojsDiagram.model as go.GraphLinksModel\r\n        ).linkDataArray.find((node) => {\r\n          return (\r\n            node['category'] === GojsNodeCategory.LinkToLink &&\r\n            node['idLink'] === linkToLink.idLink\r\n          );\r\n        });\r\n        if (existingLinkToLink) {\r\n          // Select the node using the diagram's selection manager\r\n          const part = gojsDiagram.findPartForData(existingLinkToLink);\r\n          if (part) {\r\n            gojsDiagram.clearSelection();\r\n            gojsDiagram.select(part); // select the part\r\n          }\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  selectMultipleGroupNodeExist(goJsDiagram: go.Diagram, nodeData: TreeNode[]) {\r\n    const existingParts: go.Part[] = [];\r\n\r\n    nodeData.forEach((data) => {\r\n      const matchingNode = goJsDiagram.model.nodeDataArray.find(\r\n        (node) => node['treeNodeTag'] === data.tag\r\n      );\r\n      if (matchingNode) {\r\n        const part = goJsDiagram.findPartForData(matchingNode);\r\n        if (part) {\r\n          existingParts.push(part);\r\n        }\r\n      }\r\n    });\r\n\r\n    if (existingParts.length > 0) {\r\n      goJsDiagram.clearSelection(); // Clear any previous selection\r\n      goJsDiagram.selectCollection(existingParts); // Select all matched parts\r\n      goJsDiagram.commandHandler.scrollToPart(existingParts[0]); // Optional: bring first selected node into view\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAOEC,gBAAgB,QAIX,2BAA2B;;;AAOlC,OAAM,MAAOC,iBAAiB;EAE5BC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAD9B,KAAAC,mBAAmB,GAAG,IAAIL,eAAe,CAAoB,IAAI,CAAC;EACb;EAE7D;;;;;;;;EAQAM,cAAcA,CAACC,OAA0B;IACvC,IAAI,CAACF,mBAAmB,CAACG,IAAI,CAACD,OAAO,CAAC;EACxC;EAEA;;;;;;;;EAQAE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACJ,mBAAmB,CAACK,YAAY,EAAE;EAChD;EAEA;;;;;;;;;;EAUAC,0BAA0BA,CAACC,SAAiB,EAAEC,UAAkB;IAC9D;IACA,MAAMC,UAAU,GAAGF,SAAS,CAACG,KAAK,CAAC,cAAc,CAAC;IAClD,IAAID,UAAU,EAAE;MACd,MAAME,GAAG,GAAGC,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;MACrC,MAAMI,KAAK,GAAGD,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;MACvC,MAAMK,IAAI,GAAGF,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC;MAEtC;MACA,MAAMM,aAAa,GAAGP,UAAU;MAEhC;MACA,MAAMQ,YAAY,GAAG,QAAQL,GAAG,KAAKE,KAAK,KAAKC,IAAI,KAAKC,aAAa,GAAG;MAExE,OAAOC,YAAY;KACpB,MAAM;MACL;MACA,OAAOT,SAAS,CAAC,CAAC;;EAEtB;EAEAU,sBAAsBA,CAACC,IAAS;IAC9B,OACGA,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACwB,KAAK,IAChDF,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACyB,gBAAgB,IAClD,OAAOH,IAAI,CAACI,eAAe,KAAK,QAAS;EAE/C;EACAC,4BAA4BA,CAACL,IAAS;IACpC,OACEA,IAAI,IACJA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAAC4B,WAAW,IAC9C,OAAON,IAAI,CAACO,qBAAqB,KAAK,QAAQ;EAElD;EAEAC,0BAA0BA,CAACR,IAAS;IAClC,OACEA,IAAI,KACHA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAAC+B,SAAS,IAC3CT,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACgC,SAAS,CAAC;EAEnD;EACAC,wBAAwBA,CAACX,IAAS;IAChC,OAAOA,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACkC,kBAAkB;EACtE;EACAC,uBAAuBA,CAACb,IAAS;IAC/B,OAAOA,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACoC,MAAM;EAC1D;EAEAC,cAAcA,CAACf,IAAS;IACtB,OAAOA,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKvB,gBAAgB,CAACsC,WAAW;EAC/D;EAEA;;;;;;;EAOAC,6BAA6BA,CAC3BC,KAAe,EACfC,SAA0B,EAC1BC,UAA4B;IAE5BF,KAAK,CAACG,aAAa,CAACC,OAAO,CAAEC,QAAQ,IAAI;MACvC,IAAIA,QAAQ,IAAIA,QAAQ,CAAC,OAAO,CAAC,EAAE;QACjCA,QAAQ,CAAC,OAAO,CAAC,CAACD,OAAO,CAAEE,IAAS,IAAI;UACtC,IAAIL,SAAS,CAACK,IAAI,CAAC,EAAE;YACnB,KAAK,MAAMC,GAAG,IAAIL,UAAU,EAAE;cAC5B,IAAIA,UAAU,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;gBAClCP,KAAK,CAACS,MAAM,CAAET,KAAK,IAAI;kBACrBA,KAAK,CAACU,GAAG,CAACJ,IAAI,EAAEC,GAAG,EAAEL,UAAU,CAACK,GAAG,CAAC,CAAC;gBACvC,CAAC,EAAE,IAAI,CAAC;;;;QAIhB,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOAI,wBAAwBA,CACtBX,KAAe,EACfC,SAA8B,EAC9BC,UAA4B;IAE5BF,KAAK,CAACG,aAAa,CAACC,OAAO,CAAEC,QAAQ,IAAI;MACvC,IAAIJ,SAAS,CAACI,QAAQ,CAAC,EAAE;QACvB,KAAK,MAAME,GAAG,IAAIL,UAAU,EAAE;UAC5B,IAAIA,UAAU,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;YAClCP,KAAK,CAACS,MAAM,CAAET,KAAK,IAAI;cACrBA,KAAK,CAACU,GAAG,CAACL,QAAQ,EAAEE,GAAG,EAAEL,UAAU,CAACK,GAAG,CAAC,CAAC;YAC3C,CAAC,EAAE,IAAI,CAAC;;;;IAIhB,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOAK,iBAAiBA,CACfZ,KAAe,EACfa,IAAmB,EACnBX,UAA4B;IAE5BY,MAAM,CAACC,IAAI,CAACb,UAAU,CAAC,CAACE,OAAO,CAAEG,GAAG,IAAI;MACtCP,KAAK,CAACS,MAAM,CAAEO,CAAC,IAAI;QACjBA,CAAC,CAACN,GAAG,CAACG,IAAI,EAAEN,GAAG,EAAEL,UAAU,CAACK,GAAG,CAAC,CAAC;MACnC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EAEA;;;;;EAMAU,mBAAmBA,CACjBZ,QAAyB,EACzBH,UAA4B,EAC5BgB,WAAuB;IAEvBb,QAAQ,CAACD,OAAO,CAAEtB,IAAI,IAAI;MACxB,IAAI,CAAC8B,iBAAiB,CAACM,WAAW,CAAClB,KAAK,EAAElB,IAAI,EAAEoB,UAAU,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEA;;;;;;EAMAiB,wBAAwBA,CACtBC,UAAkB,EAClBrC,QAA0B,EAC1BsC,WAAuB;IAEvB,MAAMC,YAAY,GAAGD,WAAW,CAACrB,KAAK,CAACG,aAAa,CAACoB,MAAM,CACxDzC,IAAI,IACFA,IAAI,CAAC,iBAAiB,CAAC,KAAKsC,UAAU,IACrCtC,IAAI,CAAC,UAAU,CAAC,KAAKC,QAAQ,IAC9BD,IAAI,CAAC,uBAAuB,CAAC,KAAKsC,UAAU,IAC3CtC,IAAI,CAAC,UAAU,CAAC,KAAKC,QAAS,CACnC;IACDsC,WAAW,CAACrB,KAAK,CAACwB,wBAAwB,CAACF,YAAY,CAAC;EAC1D;EAEAG,qBAAqBA,CACnBJ,WAAuB,EACvBpB,SAA8B;IAE9B,MAAMD,KAAK,GAAGqB,WAAW,CAACrB,KAA2B;IACrDA,KAAK,CAACG,aAAa,CAACC,OAAO,CAAEC,QAAQ,IAAI;MACvC,IAAIJ,SAAS,CAACI,QAAQ,CAAC,EAAE;QACvBL,KAAK,CAAC0B,cAAc,CAACrB,QAAQ,CAAC;;IAElC,CAAC,CAAC;EACJ;EAEAsB,qBAAqBA,CAAC7D,OAAmB,EAAE8D,SAA+B;IACxE,MAAMC,eAAe,GACnB/D,OAAO,CAACkC,KACT,CAAC8B,aAAa,CAACP,MAAM,CACnBQ,IAAI,IACHA,IAAI,CAAC,mBAAmB,CAAC,IAAIH,SAAS,CAAC1C,eAAe,IACtD6C,IAAI,CAAC,wBAAwB,CAAC,IAAIH,SAAS,CAAC1C,eAAe,CAC9D;IAED,IAAI2C,eAAe,EAAE;MACnBA,eAAe,CAACzB,OAAO,CAAE4B,eAAe,IAAI;QAC1C,IAAI,CAACC,gBAAgB,CACnBnE,OAAO,EACNiE,IAAI,IACHA,IAAI,CAACG,EAAE,KAAKF,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAC3CD,IAAI,CAAChD,QAAQ,KAAKvB,gBAAgB,CAAC2E,UAAU,CAChD;MACH,CAAC,CAAC;MACDrE,OAAO,CAACkC,KAA4B,CAACoC,wBAAwB,CAC5DP,eAAe,CAChB;;EAEL;EAEAI,gBAAgBA,CACdnE,OAAmB,EACnBmC,SAAiC,EACjCoC,kBAA2B;IAE3B,MAAMrC,KAAK,GAAGlC,OAAO,CAACkC,KAA2B;IACjD,MAAMsC,aAAa,GAAGtC,KAAK,CAAC8B,aAAa,CAACS,IAAI,CAACtC,SAAS,CAAC;IACzD,IAAIqC,aAAa,EAAE;MACjBtC,KAAK,CAACwC,cAAc,CAACF,aAAa,CAAC;MACnC,IAAI,CAAC3E,kBAAkB,CAACsE,gBAAgB,CAACK,aAAa,CAAC,KAAK,CAAC,CAAC;KAC/D,MAAM,IAAID,kBAAkB,EAAE;MAC7B,IAAI,CAAC1E,kBAAkB,CAAC8E,0BAA0B,CAChD,oBAAoB,EACpBJ,kBAAkB,CACnB;;EAEL;EAEAK,mBAAmBA,CAACxB,WAAuB,EAAEb,QAAkB;IAC7D,MAAMsC,YAAY,GAAGzB,WAAW,CAAClB,KAAK,CAACG,aAAa,CAACoC,IAAI,CAAEzD,IAAI,IAAI;MACjE,OAAOA,IAAI,CAAC,aAAa,CAAC,KAAKuB,QAAQ,CAACuC,GAAG;IAC7C,CAAC,CAAC;IACF,IAAID,YAAY,EAAE;MAChB;MACA,MAAME,IAAI,GAAG3B,WAAW,CAAC4B,eAAe,CAACH,YAAY,CAAC;MACtD,IAAIE,IAAI,EAAE;QACR;QACA3B,WAAW,CAAC6B,MAAM,CAACF,IAAI,CAAC,CAAC,CAAC;;MAG5B,OAAO,IAAI;KACZ,MAAM,IAAIxC,QAAQ,CAACtB,QAAQ,KAAKvB,gBAAgB,CAACyB,gBAAgB,EAAE;MAClE;MACA,MAAM+D,UAAU,GAAG,IAAI,CAACrF,kBAAkB,CACvCsF,cAAc,EAAE,CAChBV,IAAI,CACFR,IAAI,IACHA,IAAI,CAACM,kBAAkB,IACtBhC,QAAQ,CAACQ,IAA6B,CAAC3B,eAAe,CAC1D;MACH,IAAI8D,UAAU,EAAE;QACd,MAAME,kBAAkB,GACtBhC,WAAW,CAAClB,KACb,CAAC8B,aAAa,CAACS,IAAI,CAAEzD,IAAI,IAAI;UAC5B,OACEA,IAAI,CAAC,UAAU,CAAC,KAAKtB,gBAAgB,CAAC2E,UAAU,IAChDrD,IAAI,CAAC,QAAQ,CAAC,KAAKkE,UAAU,CAACG,MAAM;QAExC,CAAC,CAAC;QACF,IAAID,kBAAkB,EAAE;UACtB;UACA,MAAML,IAAI,GAAG3B,WAAW,CAAC4B,eAAe,CAACI,kBAAkB,CAAC;UAC5D,IAAIL,IAAI,EAAE;YACR3B,WAAW,CAACkC,cAAc,EAAE;YAC5BlC,WAAW,CAAC6B,MAAM,CAACF,IAAI,CAAC,CAAC,CAAC;;UAE5B,OAAO,IAAI;;;;IAIjB,OAAO,KAAK;EACd;EAEAQ,4BAA4BA,CAAChC,WAAuB,EAAEhB,QAAoB;IACxE,MAAMiD,aAAa,GAAc,EAAE;IAEnCjD,QAAQ,CAACD,OAAO,CAAES,IAAI,IAAI;MACxB,MAAM0C,YAAY,GAAGlC,WAAW,CAACrB,KAAK,CAACG,aAAa,CAACoC,IAAI,CACtDzD,IAAI,IAAKA,IAAI,CAAC,aAAa,CAAC,KAAK+B,IAAI,CAAC+B,GAAG,CAC3C;MACD,IAAIW,YAAY,EAAE;QAChB,MAAMV,IAAI,GAAGxB,WAAW,CAACyB,eAAe,CAACS,YAAY,CAAC;QACtD,IAAIV,IAAI,EAAE;UACRS,aAAa,CAACE,IAAI,CAACX,IAAI,CAAC;;;IAG9B,CAAC,CAAC;IAEF,IAAIS,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;MAC5BpC,WAAW,CAAC+B,cAAc,EAAE,CAAC,CAAC;MAC9B/B,WAAW,CAACqC,gBAAgB,CAACJ,aAAa,CAAC,CAAC,CAAC;MAC7CjC,WAAW,CAACsC,cAAc,CAACC,YAAY,CAACN,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/D;EAAC,QAAAO,CAAA,G;qBAjUUpG,iBAAiB,EAAAqG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjBzG,iBAAiB;IAAA0G,OAAA,EAAjB1G,iBAAiB,CAAA2G,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}