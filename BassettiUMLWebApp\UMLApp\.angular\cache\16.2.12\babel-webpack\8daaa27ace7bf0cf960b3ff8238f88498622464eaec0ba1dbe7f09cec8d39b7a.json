{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../api/cardinality-api.service\";\nimport * as i2 from \"src/app/shared/utils/diagram-utils\";\nexport class CardinalityService {\n  /**\n   * Creates an instance of CardinalityService.\n   * @param {CardinalityApiService} linkApiService\n   *\n   * @memberOf CardinalityService\n   */\n  constructor(linkApiService, diagramUtils) {\n    this.linkApiService = linkApiService;\n    this.diagramUtils = diagramUtils;\n    this.links = [];\n    this.linkToLinks = [];\n    this.linkTypes = new Map();\n    // Flag to track if link types have been loaded\n    this.linkTypesLoaded = false;\n  }\n  /**\n   * Initializes link types by fetching them from the API.\n   * Caches the results to avoid unnecessary API calls.\n   * @param {boolean} [forceRefresh=false] - If true, forces a refresh of the link types even if they've been loaded before\n   */\n  initLinkTypes(forceRefresh = false) {\n    // Only fetch link types if they haven't been loaded yet or if a refresh is forced\n    if (!this.linkTypesLoaded || forceRefresh) {\n      this.getAllLinkTypes().subscribe(links => {\n        const choices = [{\n          from: '0..1',\n          to: '0..1'\n        }, {\n          from: '0..1',\n          to: '1'\n        }, {\n          from: '0..1',\n          to: '*'\n        }, {\n          from: '0..1',\n          to: '1..*'\n        }, {\n          from: '1',\n          to: '0..1'\n        }, {\n          from: '1',\n          to: '1'\n        }, {\n          from: '1',\n          to: '*'\n        }, {\n          from: '1',\n          to: '1..*'\n        }, {\n          from: '*',\n          to: '0..1'\n        }, {\n          from: '*',\n          to: '1'\n        }, {\n          from: '*',\n          to: '*'\n        }, {\n          from: '*',\n          to: '1..*'\n        }, {\n          from: '1..*',\n          to: '0..1'\n        }, {\n          from: '1..*',\n          to: '1'\n        }, {\n          from: '1..*',\n          to: '*'\n        }, {\n          from: '1..*',\n          to: '1..*'\n        }];\n        links.forEach(linkType => {\n          this.linkTypes.set(linkType.id, choices[linkType.linkTypeCode]);\n        });\n        this.linkTypesLoaded = true;\n      });\n    }\n  }\n  getLinkTypes() {\n    return this.linkTypes;\n  }\n  /**\n   * Returns an array of CardinalityDetails objects representing the links.\n   * @returns {CardinalityDetails[]} An array of CardinalityDetails objects.\n   * @memberOf CardinalityService\n   */\n  getLinks() {\n    return this.links;\n  }\n  getLinkToLinks() {\n    return this.linkToLinks;\n  }\n  /**\n   * Adds a new CardinalityDetails object to the links array.\n   * @param {CardinalityDetails} link The new CardinalityDetails object to add.\n   * @memberOf CardinalityService\n   */\n  addLink(link) {\n    if (!this.links.find(l => l.id === link.id)) this.links.push(link);\n  }\n  addLinkToLink(linkToLink) {\n    if (!this.linkToLinks.find(l => l.id === linkToLink.id)) this.linkToLinks.push(linkToLink);\n  }\n  // public removeLinkToLink(idLinkToLink: number) {\n  //   this.linkToLinks = this.linkToLinks.filter(\n  //     (link) => link.id != idLinkToLink\n  //   );\n  // }\n  removeLinkToLinkByProperty(property, value) {\n    this.linkToLinks = this.linkToLinks.filter(link => link[property] !== value);\n  }\n  // public removeLinkToLinkByLinkId(idLink: number) {\n  //   this.linkToLinks = this.linkToLinks.filter((link) => link.idLink != idLink);\n  // }\n  // public removeLinkToLinkByAssociativeCls(idAssociativeClass: number) {\n  //   this.linkToLinks = this.linkToLinks.filter(\n  //     (link) => link.idAssociativeClass != idAssociativeClass\n  //   );\n  // }\n  /**\n   * Modifies an existing CardinalityDetails object in the links array.\n   * @param {CardinalityDetails} link The modified link.\n   * @memberOf CardinalityService\n   */\n  modifyLink(link) {\n    const index = this.links.findIndex(l => l.id === link.id);\n    this.links[index] = link;\n  }\n  modifyLinkToLink(linkToLink) {\n    const index = this.linkToLinks.findIndex(l => l.id === linkToLink.id);\n    this.linkToLinks[index] = linkToLink;\n  }\n  addNewLinkPort(linkId, linkPort) {\n    const existingLink = this.links.find(l => l.id === linkId);\n    if (existingLink) {\n      existingLink.linkPorts.push(linkPort);\n    }\n  }\n  removeExistingLinkPort(linkId, portId) {\n    const existingLink = this.links.find(l => l.id === linkId);\n    if (existingLink) {\n      const updatedPorts = existingLink.linkPorts.filter(p => p.id !== portId);\n      existingLink.linkPorts = updatedPorts;\n    }\n  }\n  /**\n   * Returns the CardinalityDetails object from the links array with the specified id.\n   *\n   * @param {number} id The id of the link to retrieve.\n   * @returns {CardinalityDetails} The link with the specified id.\n   *\n   * @memberOf CardinalityService\n   */\n  getLinkById(id) {\n    return this.links.find(link => link.id === id);\n  }\n  /**\n   * Adds multiple CardinalityDetails objects to the links array.\n   *\n   * @param {CardinalityDetails[]} links The cardinality objects to add.\n   *\n   * @memberOf CardinalityService\n   */\n  setLinks(links) {\n    this.links.push(...links);\n  }\n  setLinkToLinks(links) {\n    this.linkToLinks.push(...links);\n  }\n  /**\n   *  Removes all CardinalityDetails objects from the links array.\n   *\n   * @memberOf CardinalityService\n   */\n  clearLinks() {\n    this.links = [];\n  }\n  clearLinkToLinks() {\n    this.linkToLinks = [];\n  }\n  /**\n   * Removes the CardinalityDetails object from the links array with the specified id.\n   *\n   * @param {number} idLink The id of the link to remove.\n   *\n   * @memberOf CardinalityService\n   */\n  removeLink(idLink) {\n    this.links = this.links.filter(link => link.id !== idLink);\n  }\n  /**\n   * Removes multiple CardinalityDetails objects from the links array.\n   *\n   * @param {CardinalityDetails[]} links The links to remove.\n   *\n   * @memberOf CardinalityService\n   */\n  removeLinks(links) {\n    this.links = this.links.filter(link => !links.find(linkToRemove => linkToRemove.id === link.id));\n  }\n  createDeletedLinkHistory(deletedLink) {\n    this.linkApiService.createDeletedLinkHistory(deletedLink).subscribe({\n      next: response => {\n        this.diagramUtils.addDeletedLink(response);\n      },\n      error: () => {}\n    });\n  }\n  removeLinkHistory(idLinkHistory) {\n    this.linkApiService.removeDeletedLinkHistory(idLinkHistory).subscribe();\n  }\n  /**\n   * Undo link deletion\n   *\n   * @param {number} idLink link id\n   * @returns void\n   *\n   * @memberOf ClassService\n   */\n  undoLinkDeletion(idLink) {\n    this.linkApiService.undoLinkDelete(idLink).subscribe();\n  }\n  /**\n   * Get all cardinality types\n   *\n   * @returns {Observable<CardinalityTypeDTO[]>}\n   *\n   * @memberOf CardinalityService\n   */\n  getAllLinkTypes() {\n    return this.linkApiService.getAllLinkType();\n  }\n  /**\n   * Get all cardinalities for a specific class\n   *\n   * @param {number} idClass The class id to retrieve the cardinalities\n   * @returns {Observable<CardinalityDetails[]>}\n   *\n   * @memberOf CardinalityService\n   */\n  getAllLinks(idClass) {\n    return this.linkApiService.getAllLinks(idClass);\n  }\n  /**\n   * Create new cardinality\n   *\n   * @param {CardinalityDetails} link A cardinality to create\n   * @returns {Observable<CardinalityDetails>}\n   *\n   * @memberOf CardinalityService\n   */\n  createNewLink(link) {\n    return this.linkApiService.createLink(link);\n  }\n  /**\n   * Update existing link with updated data\n   *\n   * @param {CardinalityPatch} link The link to update\n   * @returns {Observable<CardinalityPatch>}\n   *\n   * @memberOf CardinalityService\n   */\n  updateLink(link) {\n    return this.linkApiService.updateLink(link);\n  }\n  /**\n   * Delete a link from the database by its ID\n   *\n   * @param {number} idLink The ID of the link to delete\n   *\n   * @memberOf CardinalityService\n   */\n  delete(idLink) {\n    this.linkApiService.deleteLink(idLink).subscribe();\n  }\n  setProjectLinks(project) {\n    const links = [];\n    project.diagrams = project.diagrams.map(diagram => {\n      return {\n        ...diagram,\n        idProject: project.id\n      };\n    });\n    this.getLinkFromFolder(project.folders, links);\n    project.templateClasses.forEach(templateClass => {\n      links.push(...templateClass.links);\n    });\n    const linkToLinks = links.filter(link => !!link.linkToLink).map(link => link.linkToLink);\n    this.setLinkToLinks(linkToLinks);\n    this.setLinks(links);\n  }\n  /**\n   * Recursively extracts links from a hierarchy of folders and appends them to the provided links array.\n   *\n   * @param folders - An array of FolderDTO objects representing the folder hierarchy.\n   * @param links - An array of CardinalityDetails objects where the extracted links will be appended.\n   */\n  getLinkFromFolder(folders, links) {\n    folders.forEach(folder => {\n      folder.templateClasses?.forEach(tempCls => {\n        links.push(...tempCls.links);\n      });\n      if (folder.childFolders.length > 0) {\n        this.getLinkFromFolder(folder.childFolders, links);\n      }\n    });\n  }\n  createLinkPort(linkPort) {\n    return this.linkApiService.createLinkPort(linkPort);\n  }\n  updateLinkPort(linkPort) {\n    return this.linkApiService.updateLinkPort(linkPort);\n  }\n  deleteLinkPort(idLinkPort) {\n    this.linkApiService.deleteLinkPort(idLinkPort).subscribe();\n  }\n  createLinkToLink(linkToLink) {\n    return this.linkApiService.createLinkToLink(linkToLink);\n  }\n  updateLinkToLink(linkToLink) {\n    return this.linkApiService.updateLinkToLink(linkToLink).subscribe(updatedLink => {\n      this.modifyLinkToLink(updatedLink);\n    });\n  }\n  /**\n   * Delete a linkToLink from the database by its ID\n   *\n   * @param {number} idLinkToLink The ID of the linkToLink to delete\n   * @memberof CardinalityService\n   */\n  deleteLinkToLink(idLinkToLink) {\n    this.linkApiService.deleteLinkToLink(idLinkToLink).subscribe();\n  }\n  /**\n   * Delete a linkToLink from the database by its ID\n   *\n   * @param {number} idLinkToLink The ID of the linkToLink to delete\n   * @memberof CardinalityService\n   */\n  undoLinkToLinkDeletion(idLinkToLink) {\n    this.linkApiService.undoLinkToLinkDelete(idLinkToLink).subscribe();\n  }\n  static #_ = this.ɵfac = function CardinalityService_Factory(t) {\n    return new (t || CardinalityService)(i0.ɵɵinject(i1.CardinalityApiService), i0.ɵɵinject(i2.DiagramUtils));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CardinalityService,\n    factory: CardinalityService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["CardinalityService", "constructor", "linkApiService", "diagramUtils", "links", "linkToLinks", "linkTypes", "Map", "linkTypesLoaded", "initLinkTypes", "forceRefresh", "getAllLinkTypes", "subscribe", "choices", "from", "to", "for<PERSON>ach", "linkType", "set", "id", "linkTypeCode", "getLinkTypes", "getLinks", "getLinkToLinks", "addLink", "link", "find", "l", "push", "addLinkToLink", "linkToLink", "removeLinkToLinkByProperty", "property", "value", "filter", "modifyLink", "index", "findIndex", "modifyLinkToLink", "addNewLinkPort", "linkId", "linkPort", "existingLink", "linkPorts", "removeExistingLinkPort", "portId", "updatedPorts", "p", "getLinkById", "setLinks", "setLinkToLinks", "clearLinks", "clearLinkToLinks", "removeLink", "idLink", "removeLinks", "linkToRemove", "createDeletedLinkHistory", "deletedLink", "next", "response", "addDeletedLink", "error", "removeLinkHistory", "idLinkHistory", "removeDeletedLinkHistory", "undoLinkDeletion", "undoLinkDelete", "getAllLinkType", "getAllLinks", "idClass", "createNewLink", "createLink", "updateLink", "delete", "deleteLink", "setProjectLinks", "project", "diagrams", "map", "diagram", "idProject", "getLinkFromFolder", "folders", "templateClasses", "templateClass", "folder", "tempCls", "childFolders", "length", "createLinkPort", "updateLinkPort", "deleteLinkPort", "idLinkPort", "createLinkToLink", "updateLinkToLink", "updatedLink", "deleteLinkToLink", "idLinkToLink", "undoLinkToLinkDeletion", "undoLinkToLinkDelete", "_", "i0", "ɵɵinject", "i1", "CardinalityApiService", "i2", "DiagramUtils", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitCodeBassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\cardinality\\cardinality.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport {\r\n  CardinalityCreate,\r\n  CardinalityDetails,\r\n  CardinalityDTO,\r\n  CardinalityPatch,\r\n  CreatedCardinality,\r\n  DeletedLink,\r\n  ILinkType,\r\n  LinkPort,\r\n  LinkPortPatch,\r\n  LinkToLink,\r\n} from 'src/app/shared/model/cardinality';\r\nimport { FolderDTO } from 'src/app/shared/model/class';\r\nimport { ProjectDetails } from 'src/app/shared/model/project';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { CardinalityApiService } from '../api/cardinality-api.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CardinalityService {\r\n  private links: CardinalityDetails[] = [];\r\n  private linkToLinks: LinkToLink[] = [];\r\n  private linkTypes: Map<number, ILinkType> = new Map<number, ILinkType>();\r\n\r\n  /**\r\n   * Creates an instance of CardinalityService.\r\n   * @param {CardinalityApiService} linkApiService\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  constructor(\r\n    private linkApiService: CardinalityApiService,\r\n    private diagramUtils: DiagramUtils\r\n  ) {}\r\n\r\n  // Flag to track if link types have been loaded\r\n  private linkTypesLoaded = false;\r\n\r\n  /**\r\n   * Initializes link types by fetching them from the API.\r\n   * Caches the results to avoid unnecessary API calls.\r\n   * @param {boolean} [forceRefresh=false] - If true, forces a refresh of the link types even if they've been loaded before\r\n   */\r\n  initLinkTypes(forceRefresh: boolean = false) {\r\n    // Only fetch link types if they haven't been loaded yet or if a refresh is forced\r\n    if (!this.linkTypesLoaded || forceRefresh) {\r\n      this.getAllLinkTypes().subscribe((links) => {\r\n        const choices: ILinkType[] = [\r\n          { from: '0..1', to: '0..1' },\r\n          { from: '0..1', to: '1' },\r\n          { from: '0..1', to: '*' },\r\n          { from: '0..1', to: '1..*' },\r\n          { from: '1', to: '0..1' },\r\n          { from: '1', to: '1' },\r\n          { from: '1', to: '*' },\r\n          { from: '1', to: '1..*' },\r\n          { from: '*', to: '0..1' },\r\n          { from: '*', to: '1' },\r\n          { from: '*', to: '*' },\r\n          { from: '*', to: '1..*' },\r\n          { from: '1..*', to: '0..1' },\r\n          { from: '1..*', to: '1' },\r\n          { from: '1..*', to: '*' },\r\n          { from: '1..*', to: '1..*' },\r\n        ];\r\n        links.forEach((linkType) => {\r\n          this.linkTypes.set(linkType.id!, choices[linkType.linkTypeCode]);\r\n        });\r\n        this.linkTypesLoaded = true;\r\n      });\r\n    }\r\n  }\r\n\r\n  public getLinkTypes(): Map<number, ILinkType> {\r\n    return this.linkTypes;\r\n  }\r\n\r\n  /**\r\n   * Returns an array of CardinalityDetails objects representing the links.\r\n   * @returns {CardinalityDetails[]} An array of CardinalityDetails objects.\r\n   * @memberOf CardinalityService\r\n   */\r\n  public getLinks(): CardinalityDetails[] {\r\n    return this.links;\r\n  }\r\n\r\n  public getLinkToLinks(): LinkToLink[] {\r\n    return this.linkToLinks;\r\n  }\r\n\r\n  /**\r\n   * Adds a new CardinalityDetails object to the links array.\r\n   * @param {CardinalityDetails} link The new CardinalityDetails object to add.\r\n   * @memberOf CardinalityService\r\n   */\r\n  public addLink(link: CardinalityDetails) {\r\n    if (!this.links.find((l) => l.id === link.id)) this.links.push(link);\r\n  }\r\n\r\n  public addLinkToLink(linkToLink: LinkToLink) {\r\n    if (!this.linkToLinks.find((l) => l.id === linkToLink.id))\r\n      this.linkToLinks.push(linkToLink);\r\n  }\r\n  // public removeLinkToLink(idLinkToLink: number) {\r\n  //   this.linkToLinks = this.linkToLinks.filter(\r\n  //     (link) => link.id != idLinkToLink\r\n  //   );\r\n  // }\r\n  private removeLinkToLinkByProperty<K extends keyof LinkToLink>(\r\n    property: K,\r\n    value: LinkToLink[K]\r\n  ) {\r\n    this.linkToLinks = this.linkToLinks.filter(\r\n      (link) => link[property] !== value\r\n    );\r\n  }\r\n  // public removeLinkToLinkByLinkId(idLink: number) {\r\n  //   this.linkToLinks = this.linkToLinks.filter((link) => link.idLink != idLink);\r\n  // }\r\n\r\n  // public removeLinkToLinkByAssociativeCls(idAssociativeClass: number) {\r\n  //   this.linkToLinks = this.linkToLinks.filter(\r\n  //     (link) => link.idAssociativeClass != idAssociativeClass\r\n  //   );\r\n  // }\r\n\r\n  /**\r\n   * Modifies an existing CardinalityDetails object in the links array.\r\n   * @param {CardinalityDetails} link The modified link.\r\n   * @memberOf CardinalityService\r\n   */\r\n  public modifyLink(link: CardinalityDetails) {\r\n    const index = this.links.findIndex((l) => l.id === link.id);\r\n    this.links[index] = link;\r\n  }\r\n  public modifyLinkToLink(linkToLink: LinkToLink) {\r\n    const index = this.linkToLinks.findIndex((l) => l.id === linkToLink.id);\r\n    this.linkToLinks[index] = linkToLink;\r\n  }\r\n\r\n  public addNewLinkPort(linkId: number, linkPort: LinkPort) {\r\n    const existingLink = this.links.find((l) => l.id === linkId);\r\n    if (existingLink) {\r\n      existingLink.linkPorts.push(linkPort);\r\n    }\r\n  }\r\n  public removeExistingLinkPort(linkId: number, portId: number) {\r\n    const existingLink = this.links.find((l) => l.id === linkId);\r\n    if (existingLink) {\r\n      const updatedPorts: LinkPort[] = existingLink.linkPorts.filter(\r\n        (p) => p.id !== portId\r\n      );\r\n      existingLink.linkPorts = updatedPorts;\r\n    }\r\n  }\r\n  /**\r\n   * Returns the CardinalityDetails object from the links array with the specified id.\r\n   *\r\n   * @param {number} id The id of the link to retrieve.\r\n   * @returns {CardinalityDetails} The link with the specified id.\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  getLinkById(id: number): CardinalityDetails {\r\n    return this.links.find((link) => link.id === id)!;\r\n  }\r\n\r\n  /**\r\n   * Adds multiple CardinalityDetails objects to the links array.\r\n   *\r\n   * @param {CardinalityDetails[]} links The cardinality objects to add.\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  public setLinks(links: CardinalityDetails[]) {\r\n    this.links.push(...links);\r\n  }\r\n  public setLinkToLinks(links: LinkToLink[]) {\r\n    this.linkToLinks.push(...links);\r\n  }\r\n\r\n  /**\r\n   *  Removes all CardinalityDetails objects from the links array.\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  public clearLinks() {\r\n    this.links = [];\r\n  }\r\n\r\n  public clearLinkToLinks() {\r\n    this.linkToLinks = [];\r\n  }\r\n\r\n  /**\r\n   * Removes the CardinalityDetails object from the links array with the specified id.\r\n   *\r\n   * @param {number} idLink The id of the link to remove.\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  public removeLink(idLink: number) {\r\n    this.links = this.links.filter((link) => link.id !== idLink);\r\n  }\r\n\r\n  /**\r\n   * Removes multiple CardinalityDetails objects from the links array.\r\n   *\r\n   * @param {CardinalityDetails[]} links The links to remove.\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  public removeLinks(links: CardinalityDetails[]) {\r\n    this.links = this.links.filter(\r\n      (link) => !links.find((linkToRemove) => linkToRemove.id === link.id)\r\n    );\r\n  }\r\n\r\n  public createDeletedLinkHistory(deletedLink: DeletedLink): void {\r\n    this.linkApiService.createDeletedLinkHistory(deletedLink).subscribe({\r\n      next: (response) => {\r\n        this.diagramUtils.addDeletedLink(response);\r\n      },\r\n      error: () => {},\r\n    });\r\n  }\r\n\r\n  public removeLinkHistory(idLinkHistory: number): void {\r\n    this.linkApiService.removeDeletedLinkHistory(idLinkHistory).subscribe();\r\n  }\r\n\r\n  /**\r\n   * Undo link deletion\r\n   *\r\n   * @param {number} idLink link id\r\n   * @returns void\r\n   *\r\n   * @memberOf ClassService\r\n   */\r\n  undoLinkDeletion(idLink: number): void {\r\n    this.linkApiService.undoLinkDelete(idLink).subscribe();\r\n  }\r\n  /**\r\n   * Get all cardinality types\r\n   *\r\n   * @returns {Observable<CardinalityTypeDTO[]>}\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  getAllLinkTypes(): Observable<CardinalityDTO[]> {\r\n    return this.linkApiService.getAllLinkType();\r\n  }\r\n\r\n  /**\r\n   * Get all cardinalities for a specific class\r\n   *\r\n   * @param {number} idClass The class id to retrieve the cardinalities\r\n   * @returns {Observable<CardinalityDetails[]>}\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  getAllLinks(idClass: number): Observable<CardinalityDetails[]> {\r\n    return this.linkApiService.getAllLinks(idClass);\r\n  }\r\n\r\n  /**\r\n   * Create new cardinality\r\n   *\r\n   * @param {CardinalityDetails} link A cardinality to create\r\n   * @returns {Observable<CardinalityDetails>}\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  createNewLink(link: CardinalityCreate): Observable<CreatedCardinality> {\r\n    return this.linkApiService.createLink(link);\r\n  }\r\n\r\n  /**\r\n   * Update existing link with updated data\r\n   *\r\n   * @param {CardinalityPatch} link The link to update\r\n   * @returns {Observable<CardinalityPatch>}\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  updateLink(link: CardinalityPatch): Observable<CardinalityPatch> {\r\n    return this.linkApiService.updateLink(link);\r\n  }\r\n\r\n  /**\r\n   * Delete a link from the database by its ID\r\n   *\r\n   * @param {number} idLink The ID of the link to delete\r\n   *\r\n   * @memberOf CardinalityService\r\n   */\r\n  delete(idLink: number): void {\r\n    this.linkApiService.deleteLink(idLink).subscribe();\r\n  }\r\n\r\n  setProjectLinks(project: ProjectDetails): void {\r\n    const links: CardinalityDetails[] = [];\r\n    project.diagrams = project.diagrams.map((diagram) => {\r\n      return { ...diagram, idProject: project.id! };\r\n    });\r\n    this.getLinkFromFolder(project.folders, links);\r\n    project.templateClasses.forEach((templateClass) => {\r\n      links.push(...templateClass.links);\r\n    });\r\n    const linkToLinks: LinkToLink[] = links\r\n      .filter((link) => !!link.linkToLink)\r\n      .map((link) => link.linkToLink!);\r\n    this.setLinkToLinks(linkToLinks);\r\n    this.setLinks(links);\r\n  }\r\n\r\n  /**\r\n   * Recursively extracts links from a hierarchy of folders and appends them to the provided links array.\r\n   *\r\n   * @param folders - An array of FolderDTO objects representing the folder hierarchy.\r\n   * @param links - An array of CardinalityDetails objects where the extracted links will be appended.\r\n   */\r\n  private getLinkFromFolder(\r\n    folders: FolderDTO[],\r\n    links: CardinalityDetails[]\r\n  ): void {\r\n    folders.forEach((folder) => {\r\n      folder.templateClasses?.forEach((tempCls) => {\r\n        links.push(...tempCls.links);\r\n      });\r\n      if (folder.childFolders.length > 0) {\r\n        this.getLinkFromFolder(folder.childFolders, links);\r\n      }\r\n    });\r\n  }\r\n\r\n  createLinkPort(linkPort: LinkPort): Observable<LinkPort> {\r\n    return this.linkApiService.createLinkPort(linkPort);\r\n  }\r\n  updateLinkPort(linkPort: LinkPortPatch): Observable<LinkPort> {\r\n    return this.linkApiService.updateLinkPort(linkPort);\r\n  }\r\n  deleteLinkPort(idLinkPort: number): void {\r\n    this.linkApiService.deleteLinkPort(idLinkPort).subscribe();\r\n  }\r\n  createLinkToLink(linkToLink: LinkToLink): Observable<LinkToLink> {\r\n    return this.linkApiService.createLinkToLink(linkToLink);\r\n  }\r\n  updateLinkToLink(linkToLink: LinkToLink) {\r\n    return this.linkApiService\r\n      .updateLinkToLink(linkToLink)\r\n      .subscribe((updatedLink) => {\r\n        this.modifyLinkToLink(updatedLink);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Delete a linkToLink from the database by its ID\r\n   *\r\n   * @param {number} idLinkToLink The ID of the linkToLink to delete\r\n   * @memberof CardinalityService\r\n   */\r\n  deleteLinkToLink(idLinkToLink: number): void {\r\n    this.linkApiService.deleteLinkToLink(idLinkToLink).subscribe();\r\n  }\r\n\r\n  /**\r\n   * Delete a linkToLink from the database by its ID\r\n   *\r\n   * @param {number} idLinkToLink The ID of the linkToLink to delete\r\n   * @memberof CardinalityService\r\n   */\r\n  undoLinkToLinkDeletion(idLinkToLink: number): void {\r\n    this.linkApiService.undoLinkToLinkDelete(idLinkToLink).subscribe();\r\n  }\r\n}\r\n"], "mappings": ";;;AAsBA,OAAM,MAAOA,kBAAkB;EAK7B;;;;;;EAMAC,YACUC,cAAqC,EACrCC,YAA0B;IAD1B,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IAZd,KAAAC,KAAK,GAAyB,EAAE;IAChC,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,SAAS,GAA2B,IAAIC,GAAG,EAAqB;IAaxE;IACQ,KAAAC,eAAe,GAAG,KAAK;EAH5B;EAKH;;;;;EAKAC,aAAaA,CAACC,YAAA,GAAwB,KAAK;IACzC;IACA,IAAI,CAAC,IAAI,CAACF,eAAe,IAAIE,YAAY,EAAE;MACzC,IAAI,CAACC,eAAe,EAAE,CAACC,SAAS,CAAER,KAAK,IAAI;QACzC,MAAMS,OAAO,GAAgB,CAC3B;UAAEC,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAM,CAAE,EAC5B;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAG,CAAE,EACzB;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAG,CAAE,EACzB;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAM,CAAE,EAC5B;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAM,CAAE,EACzB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAG,CAAE,EACtB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAG,CAAE,EACtB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAM,CAAE,EACzB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAM,CAAE,EACzB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAG,CAAE,EACtB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAG,CAAE,EACtB;UAAED,IAAI,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAM,CAAE,EACzB;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAM,CAAE,EAC5B;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAG,CAAE,EACzB;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAG,CAAE,EACzB;UAAED,IAAI,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAM,CAAE,CAC7B;QACDX,KAAK,CAACY,OAAO,CAAEC,QAAQ,IAAI;UACzB,IAAI,CAACX,SAAS,CAACY,GAAG,CAACD,QAAQ,CAACE,EAAG,EAAEN,OAAO,CAACI,QAAQ,CAACG,YAAY,CAAC,CAAC;QAClE,CAAC,CAAC;QACF,IAAI,CAACZ,eAAe,GAAG,IAAI;MAC7B,CAAC,CAAC;;EAEN;EAEOa,YAAYA,CAAA;IACjB,OAAO,IAAI,CAACf,SAAS;EACvB;EAEA;;;;;EAKOgB,QAAQA,CAAA;IACb,OAAO,IAAI,CAAClB,KAAK;EACnB;EAEOmB,cAAcA,CAAA;IACnB,OAAO,IAAI,CAAClB,WAAW;EACzB;EAEA;;;;;EAKOmB,OAAOA,CAACC,IAAwB;IACrC,IAAI,CAAC,IAAI,CAACrB,KAAK,CAACsB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKM,IAAI,CAACN,EAAE,CAAC,EAAE,IAAI,CAACf,KAAK,CAACwB,IAAI,CAACH,IAAI,CAAC;EACtE;EAEOI,aAAaA,CAACC,UAAsB;IACzC,IAAI,CAAC,IAAI,CAACzB,WAAW,CAACqB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKW,UAAU,CAACX,EAAE,CAAC,EACvD,IAAI,CAACd,WAAW,CAACuB,IAAI,CAACE,UAAU,CAAC;EACrC;EACA;EACA;EACA;EACA;EACA;EACQC,0BAA0BA,CAChCC,QAAW,EACXC,KAAoB;IAEpB,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC6B,MAAM,CACvCT,IAAI,IAAKA,IAAI,CAACO,QAAQ,CAAC,KAAKC,KAAK,CACnC;EACH;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA;;;;;EAKOE,UAAUA,CAACV,IAAwB;IACxC,MAAMW,KAAK,GAAG,IAAI,CAAChC,KAAK,CAACiC,SAAS,CAAEV,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKM,IAAI,CAACN,EAAE,CAAC;IAC3D,IAAI,CAACf,KAAK,CAACgC,KAAK,CAAC,GAAGX,IAAI;EAC1B;EACOa,gBAAgBA,CAACR,UAAsB;IAC5C,MAAMM,KAAK,GAAG,IAAI,CAAC/B,WAAW,CAACgC,SAAS,CAAEV,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKW,UAAU,CAACX,EAAE,CAAC;IACvE,IAAI,CAACd,WAAW,CAAC+B,KAAK,CAAC,GAAGN,UAAU;EACtC;EAEOS,cAAcA,CAACC,MAAc,EAAEC,QAAkB;IACtD,MAAMC,YAAY,GAAG,IAAI,CAACtC,KAAK,CAACsB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKqB,MAAM,CAAC;IAC5D,IAAIE,YAAY,EAAE;MAChBA,YAAY,CAACC,SAAS,CAACf,IAAI,CAACa,QAAQ,CAAC;;EAEzC;EACOG,sBAAsBA,CAACJ,MAAc,EAAEK,MAAc;IAC1D,MAAMH,YAAY,GAAG,IAAI,CAACtC,KAAK,CAACsB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACR,EAAE,KAAKqB,MAAM,CAAC;IAC5D,IAAIE,YAAY,EAAE;MAChB,MAAMI,YAAY,GAAeJ,YAAY,CAACC,SAAS,CAACT,MAAM,CAC3Da,CAAC,IAAKA,CAAC,CAAC5B,EAAE,KAAK0B,MAAM,CACvB;MACDH,YAAY,CAACC,SAAS,GAAGG,YAAY;;EAEzC;EACA;;;;;;;;EAQAE,WAAWA,CAAC7B,EAAU;IACpB,OAAO,IAAI,CAACf,KAAK,CAACsB,IAAI,CAAED,IAAI,IAAKA,IAAI,CAACN,EAAE,KAAKA,EAAE,CAAE;EACnD;EAEA;;;;;;;EAOO8B,QAAQA,CAAC7C,KAA2B;IACzC,IAAI,CAACA,KAAK,CAACwB,IAAI,CAAC,GAAGxB,KAAK,CAAC;EAC3B;EACO8C,cAAcA,CAAC9C,KAAmB;IACvC,IAAI,CAACC,WAAW,CAACuB,IAAI,CAAC,GAAGxB,KAAK,CAAC;EACjC;EAEA;;;;;EAKO+C,UAAUA,CAAA;IACf,IAAI,CAAC/C,KAAK,GAAG,EAAE;EACjB;EAEOgD,gBAAgBA,CAAA;IACrB,IAAI,CAAC/C,WAAW,GAAG,EAAE;EACvB;EAEA;;;;;;;EAOOgD,UAAUA,CAACC,MAAc;IAC9B,IAAI,CAAClD,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC8B,MAAM,CAAET,IAAI,IAAKA,IAAI,CAACN,EAAE,KAAKmC,MAAM,CAAC;EAC9D;EAEA;;;;;;;EAOOC,WAAWA,CAACnD,KAA2B;IAC5C,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC8B,MAAM,CAC3BT,IAAI,IAAK,CAACrB,KAAK,CAACsB,IAAI,CAAE8B,YAAY,IAAKA,YAAY,CAACrC,EAAE,KAAKM,IAAI,CAACN,EAAE,CAAC,CACrE;EACH;EAEOsC,wBAAwBA,CAACC,WAAwB;IACtD,IAAI,CAACxD,cAAc,CAACuD,wBAAwB,CAACC,WAAW,CAAC,CAAC9C,SAAS,CAAC;MAClE+C,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACzD,YAAY,CAAC0D,cAAc,CAACD,QAAQ,CAAC;MAC5C,CAAC;MACDE,KAAK,EAAEA,CAAA,KAAK,CAAE;KACf,CAAC;EACJ;EAEOC,iBAAiBA,CAACC,aAAqB;IAC5C,IAAI,CAAC9D,cAAc,CAAC+D,wBAAwB,CAACD,aAAa,CAAC,CAACpD,SAAS,EAAE;EACzE;EAEA;;;;;;;;EAQAsD,gBAAgBA,CAACZ,MAAc;IAC7B,IAAI,CAACpD,cAAc,CAACiE,cAAc,CAACb,MAAM,CAAC,CAAC1C,SAAS,EAAE;EACxD;EACA;;;;;;;EAOAD,eAAeA,CAAA;IACb,OAAO,IAAI,CAACT,cAAc,CAACkE,cAAc,EAAE;EAC7C;EAEA;;;;;;;;EAQAC,WAAWA,CAACC,OAAe;IACzB,OAAO,IAAI,CAACpE,cAAc,CAACmE,WAAW,CAACC,OAAO,CAAC;EACjD;EAEA;;;;;;;;EAQAC,aAAaA,CAAC9C,IAAuB;IACnC,OAAO,IAAI,CAACvB,cAAc,CAACsE,UAAU,CAAC/C,IAAI,CAAC;EAC7C;EAEA;;;;;;;;EAQAgD,UAAUA,CAAChD,IAAsB;IAC/B,OAAO,IAAI,CAACvB,cAAc,CAACuE,UAAU,CAAChD,IAAI,CAAC;EAC7C;EAEA;;;;;;;EAOAiD,MAAMA,CAACpB,MAAc;IACnB,IAAI,CAACpD,cAAc,CAACyE,UAAU,CAACrB,MAAM,CAAC,CAAC1C,SAAS,EAAE;EACpD;EAEAgE,eAAeA,CAACC,OAAuB;IACrC,MAAMzE,KAAK,GAAyB,EAAE;IACtCyE,OAAO,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAACC,GAAG,CAAEC,OAAO,IAAI;MAClD,OAAO;QAAE,GAAGA,OAAO;QAAEC,SAAS,EAAEJ,OAAO,CAAC1D;MAAG,CAAE;IAC/C,CAAC,CAAC;IACF,IAAI,CAAC+D,iBAAiB,CAACL,OAAO,CAACM,OAAO,EAAE/E,KAAK,CAAC;IAC9CyE,OAAO,CAACO,eAAe,CAACpE,OAAO,CAAEqE,aAAa,IAAI;MAChDjF,KAAK,CAACwB,IAAI,CAAC,GAAGyD,aAAa,CAACjF,KAAK,CAAC;IACpC,CAAC,CAAC;IACF,MAAMC,WAAW,GAAiBD,KAAK,CACpC8B,MAAM,CAAET,IAAI,IAAK,CAAC,CAACA,IAAI,CAACK,UAAU,CAAC,CACnCiD,GAAG,CAAEtD,IAAI,IAAKA,IAAI,CAACK,UAAW,CAAC;IAClC,IAAI,CAACoB,cAAc,CAAC7C,WAAW,CAAC;IAChC,IAAI,CAAC4C,QAAQ,CAAC7C,KAAK,CAAC;EACtB;EAEA;;;;;;EAMQ8E,iBAAiBA,CACvBC,OAAoB,EACpB/E,KAA2B;IAE3B+E,OAAO,CAACnE,OAAO,CAAEsE,MAAM,IAAI;MACzBA,MAAM,CAACF,eAAe,EAAEpE,OAAO,CAAEuE,OAAO,IAAI;QAC1CnF,KAAK,CAACwB,IAAI,CAAC,GAAG2D,OAAO,CAACnF,KAAK,CAAC;MAC9B,CAAC,CAAC;MACF,IAAIkF,MAAM,CAACE,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACP,iBAAiB,CAACI,MAAM,CAACE,YAAY,EAAEpF,KAAK,CAAC;;IAEtD,CAAC,CAAC;EACJ;EAEAsF,cAAcA,CAACjD,QAAkB;IAC/B,OAAO,IAAI,CAACvC,cAAc,CAACwF,cAAc,CAACjD,QAAQ,CAAC;EACrD;EACAkD,cAAcA,CAAClD,QAAuB;IACpC,OAAO,IAAI,CAACvC,cAAc,CAACyF,cAAc,CAAClD,QAAQ,CAAC;EACrD;EACAmD,cAAcA,CAACC,UAAkB;IAC/B,IAAI,CAAC3F,cAAc,CAAC0F,cAAc,CAACC,UAAU,CAAC,CAACjF,SAAS,EAAE;EAC5D;EACAkF,gBAAgBA,CAAChE,UAAsB;IACrC,OAAO,IAAI,CAAC5B,cAAc,CAAC4F,gBAAgB,CAAChE,UAAU,CAAC;EACzD;EACAiE,gBAAgBA,CAACjE,UAAsB;IACrC,OAAO,IAAI,CAAC5B,cAAc,CACvB6F,gBAAgB,CAACjE,UAAU,CAAC,CAC5BlB,SAAS,CAAEoF,WAAW,IAAI;MACzB,IAAI,CAAC1D,gBAAgB,CAAC0D,WAAW,CAAC;IACpC,CAAC,CAAC;EACN;EAEA;;;;;;EAMAC,gBAAgBA,CAACC,YAAoB;IACnC,IAAI,CAAChG,cAAc,CAAC+F,gBAAgB,CAACC,YAAY,CAAC,CAACtF,SAAS,EAAE;EAChE;EAEA;;;;;;EAMAuF,sBAAsBA,CAACD,YAAoB;IACzC,IAAI,CAAChG,cAAc,CAACkG,oBAAoB,CAACF,YAAY,CAAC,CAACtF,SAAS,EAAE;EACpE;EAAC,QAAAyF,CAAA,G;qBAnWUrG,kBAAkB,EAAAsG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAlB5G,kBAAkB;IAAA6G,OAAA,EAAlB7G,kBAAkB,CAAA8G,IAAA;IAAAC,UAAA,EAFjB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}