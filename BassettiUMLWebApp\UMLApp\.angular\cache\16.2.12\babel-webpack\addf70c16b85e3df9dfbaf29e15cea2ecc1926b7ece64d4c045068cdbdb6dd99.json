{"ast": null, "code": "import go from 'gojs';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../access/access.service\";\nimport * as i2 from \"../class/class.service\";\nimport * as i3 from \"../gojs/gojsCardinality/gojs-cardinality.service\";\nimport * as i4 from \"../property/property.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nimport * as i6 from \"../gojs/gojsCommon/gojs-common.service\";\nimport * as i7 from \"../gojs/gojsEnumeration/gojs-enumeration.service\";\nimport * as i8 from \"../enumeration/enumeration.service\";\nimport * as i9 from \"../gojs/gojs-comment/gojs-comment.service\";\nimport * as i10 from \"../gojs/gojsAttribute/gojs-attribute.service\";\nimport * as i11 from \"../gojs/gojsClass/gojs-class.service\";\nimport * as i12 from \"../gojs/gojsLiteral/gojs-literal.service\";\nimport * as i13 from \"../comment/comment.service\";\nexport class EventListenerService {\n  constructor(accessService, classService, goJsCardinalityService, propertyService, diagramUtils, commonGojsService, goJsEnumerationService, enumerationService, gojsCommentService, goJsAttrService, gojsClassService, goJsLiteralService, commentService) {\n    this.accessService = accessService;\n    this.classService = classService;\n    this.goJsCardinalityService = goJsCardinalityService;\n    this.propertyService = propertyService;\n    this.diagramUtils = diagramUtils;\n    this.commonGojsService = commonGojsService;\n    this.goJsEnumerationService = goJsEnumerationService;\n    this.enumerationService = enumerationService;\n    this.gojsCommentService = gojsCommentService;\n    this.goJsAttrService = goJsAttrService;\n    this.gojsClassService = gojsClassService;\n    this.goJsLiteralService = goJsLiteralService;\n    this.commentService = commentService;\n    /**\n     * Selection delete event listener for deleting the selective class\n     * @private\n     * @param {go.DiagramEvent} event\n     * @memberof DiagramEditorComponent\n     */\n    this.addDeletingEventListener = event => {\n      if (this.hasEditAccessOnly) {\n        const nodes = event.subject;\n        const classIds = [],\n          enumIds = [];\n        // Check if any of the selected nodes are links (associations)\n        let hasLinks = false;\n        nodes.each(node => {\n          if (node.data.category == GojsNodeCategory.Association) {\n            hasLinks = true;\n          }\n        });\n        // Cancel the event initially\n        event.cancel = true;\n        // If there are links, handle them with confirmation\n        if (hasLinks) {\n          this.handleLinkDeletion(nodes, event);\n        } else {\n          // For non-link nodes, proceed with immediate deletion\n          this.handleNonLinkDeletion(nodes, classIds, enumIds);\n        }\n      }\n    };\n    this.accessService.accessTypeChanges().subscribe(response => {\n      if (response != AccessType.Viewer) {\n        this.hasEditAccessOnly = true;\n      } else {\n        this.hasEditAccessOnly = false;\n      }\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n    this.commonGojsService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this.diagram = diagram;\n    });\n  }\n  /**\n   * Adds event listeners to the diagram for handling model changes and other interactions.\n   * @memberof DiagramEditorComponent\n   */\n  addDiagramEventListeners() {\n    this.addSelectionDeletingListener();\n    this.addPartResizedListener();\n    this.addTextEditedListener();\n    this.addLinkDrawnListener();\n    this.addLinkReDrawnListener();\n    this.addModelChangedListener();\n  }\n  /**\n   * Adds a listener for the 'SelectionDeleting' event on the diagram.\n   * @private\n   */\n  addSelectionDeletingListener() {\n    this.diagram.addDiagramListener('SelectionDeleting', this.addDeletingEventListener);\n  }\n  /**\n   * Handles deletion of link nodes with confirmation\n   * @private\n   * @param {go.Set<go.Node>} nodes - The nodes to be deleted\n   * @param {go.DiagramEvent} event - The diagram event\n   */\n  handleLinkDeletion(nodes, event) {\n    nodes.each(node => {\n      if (node.data.category == GojsNodeCategory.Association) {\n        // Call deleteLink which will handle the confirmation dialog\n        this.goJsCardinalityService.deleteLink(node, nodes.count, this.diagram, event);\n      }\n    });\n  }\n  /**\n   * Handles deletion of non-link nodes (classes, enums, comments)\n   * @private\n   * @param {go.Set<go.Node>} nodes - The nodes to be deleted\n   * @param {number[]} classIds - Array to collect class IDs\n   * @param {number[]} enumIds - Array to collect enum IDs\n   */\n  handleNonLinkDeletion(nodes, classIds, enumIds) {\n    nodes.each(node => {\n      if (node.data.category == GojsNodeCategory.Class || node.data.category == GojsNodeCategory.AssociativeClass) {\n        classIds.push(node.data.id);\n      } else if (node.data.category == GojsNodeCategory.Enumeration) {\n        enumIds.push(node.data.id);\n      } else if (node.data.category == GojsNodeCategory.Comment) {\n        this.gojsCommentService.handleCommentDelete([node.data.id]);\n      }\n    });\n    if (classIds.length > 0) this.classService.deleteClasses(classIds);\n    if (enumIds.length > 0) this.enumerationService.deleteEnumerations(enumIds);\n    this.propertyService.setPropertyData(null);\n  }\n  /**\n   * Adds a listener for the 'PartResized' event on the diagram.\n   * @private\n   */\n  addPartResizedListener() {\n    this.diagram.addDiagramListener('PartResized', e => {\n      if (this.hasEditAccessOnly) {\n        this.handlePartResized(e);\n      }\n    });\n  }\n  /**\n   * Handles the 'PartResized' event on the diagram.\n   * @private\n   * @param {go.DiagramEvent} event - The event object.\n   */\n  handlePartResized(event) {\n    const shape = event.subject;\n    let diagramData = event.diagram.selection.first()?.data;\n    diagramData.size = new go.Size(shape.actualBounds.width, shape.actualBounds.height);\n    if (diagramData.category === GojsNodeCategory.Class || diagramData.category === GojsNodeCategory.AssociativeClass) {\n      this.updateClass(diagramData);\n    } else if (diagramData.category === GojsNodeCategory.Enumeration) {\n      this.goJsEnumerationService.updateExistingEnum(diagramData, false);\n    } else if (diagramData.category === GojsNodeCategory.Comment) {\n      this.gojsCommentService.updateComment(diagramData.name, diagramData.description, diagramData, event.diagram);\n    }\n  }\n  /**\n   * Updates the class based on the resized data.\n   * @private\n   * @param {GojsDiagramClassNode} diagramData - The data of the resized diagram element.\n   */\n  updateClass(diagramData) {\n    this.classService.updateClass({\n      id: diagramData.id,\n      name: diagramData.name,\n      idDiagram: this.currentDiagram.id,\n      key: diagramData.key,\n      isAssociative: diagramData.isAssociative,\n      property: {\n        position: diagramData.position,\n        height: diagramData.size.height,\n        width: diagramData.size.width,\n        icon: diagramData.icon,\n        color: diagramData.color\n      }\n    });\n  }\n  /**\n   * Adds a listener for the 'TextEdited' event on the diagram.\n   * @private\n   */\n  addTextEditedListener() {\n    this.diagram.addDiagramListener('TextEdited', e => {\n      if (e.subject.panel && (e.subject.panel.data || e.diagram.selection.first()?.data.category === GojsNodeCategory.Association) && this.hasEditAccessOnly) {\n        this.handleTextEditedListener(e);\n      }\n    });\n  }\n  /**\n   * Handles the editing of text elements on the diagram.\n   * Updates links or attributes based on the edited data.\n   * @private\n   * @param {go.DiagramEvent} event - The event object.\n   */\n  handleTextEditedListener(event) {\n    if (event.diagram.selection && event.diagram.selection.first()?.data && event.diagram.selection.first()?.data.category == GojsNodeCategory.Association) {\n      this.goJsCardinalityService.updateLinkOnTextEdited(event.diagram.selection.first()?.data);\n    } else if (event.subject.panel.data.category == GojsNodeCategory.Attribute || event.subject.panel.data.category == GojsNodeCategory.Operation) {\n      this.goJsAttrService.updateAttributeOnTextEdited(event, this.diagram);\n    } else if (event.subject.panel.data.category == GojsNodeCategory.EnumerationLiteral) {\n      this.goJsLiteralService.handleEditLiteralNameInDiagram(event, this.diagram);\n    }\n  }\n  /**\n   * Adds a listener for the 'LinkDrawn' event on the diagram.\n   * @private\n   */\n  addLinkDrawnListener() {\n    this.diagram.addDiagramListener('LinkDrawn', event => {\n      if (this.hasEditAccessOnly) {\n        this.handleLinkDrawn(event);\n      }\n    });\n  }\n  /**\n   * Handles the event when a link is drawn on the diagram.\n   * Checks if the link is between valid nodes and creates or updates the link accordingly.\n   * @private\n   * @param {go.DiagramEvent} event - The event object.\n   */\n  handleLinkDrawn(event) {\n    const linkData = event.subject.data;\n    const toNode = this.diagramUtils.getObjectDataByKey(this.diagram, linkData.to);\n    const fromNode = this.diagramUtils.getObjectDataByKey(this.diagram, linkData.from);\n    if (toNode && fromNode) {\n      if (fromNode['category'] == GojsNodeCategory.AssociativeClass && toNode['category'] == GojsNodeCategory.LinkLabel) {\n        const linkToLinkObj = this.createLinkToLinkObject(fromNode, toNode, linkData);\n        this.goJsCardinalityService.createNewLinkToLinkAndUpdate(linkData, linkToLinkObj, this.diagram);\n      } else {\n        const deletedLink = this.findDeletedLink(fromNode, toNode);\n        const newLink = this.createLinkObject(fromNode, toNode, linkData);\n        if (deletedLink) {\n          this.goJsCardinalityService.handleDeletedLink(deletedLink, linkData, newLink, fromNode, toNode, this.diagram);\n        } else {\n          this.goJsCardinalityService.createNewLinkAndUpdate(linkData, newLink, fromNode, toNode, this.diagram);\n          event.diagram.clearSelection();\n        }\n      }\n    }\n  }\n  /**\n   * Finds a deleted link between two nodes.\n   * @private\n   * @param {go.ObjectData} fromNode - The source node.\n   * @param {go.ObjectData} toNode - The destination node.\n   * @returns {DeletedLink | undefined } The deleted link if found, otherwise null.\n   */\n  findDeletedLink(fromNode, toNode) {\n    return this.diagramUtils.getDeletedLinks().find(link => link.idSourceClass == fromNode['id'] && link.idDestinationClass == toNode['id'] || link.idSourceClass == toNode['id'] && link.idDestinationClass == fromNode['id']);\n  }\n  /**\n   * Creates a new link object based on the source and destination nodes.\n   * @private\n   * @param {go.ObjectData} fromNode - The source node.\n   * @param {go.ObjectData} toNode - The destination node.\n   * @param {GojsLinkNode} linkData - The link data.\n   * @returns {CardinalityDetails} The new link object.\n   */\n  createLinkObject(fromNode, toNode, linkData) {\n    return {\n      name: `${fromNode['name']} - ${toNode['name']}`,\n      id: linkData.id,\n      idSourceTempClass: fromNode['idTemplateClass'],\n      idDestinationTempClass: toNode['idTemplateClass'],\n      idLinkType: 1,\n      sourcePort: linkData.fromPort,\n      destinationPort: linkData.toPort,\n      idDiagram: this.currentDiagram.id,\n      color: 'rgba(0, 0, 0, 1)',\n      fromComment: linkData.fromComment,\n      toComment: linkData.toComment,\n      segmentOffset: '0 0',\n      linkPort: {\n        idDiagram: this.currentDiagram.id,\n        destinationPort: linkData.toPort,\n        sourcePort: linkData.fromPort,\n        idLink: linkData.id,\n        segmentOffset: '0 0'\n      }\n    };\n  }\n  createLinkToLinkObject(fromNode, toNode, linkData) {\n    return {\n      idLink: toNode['idLink'],\n      idAssociativeClass: fromNode['idTemplateClass'],\n      port: linkData.fromPort\n    };\n  }\n  addLinkReDrawnListener() {\n    this.diagram.addDiagramListener('LinkRelinked', event => {\n      if (this.hasEditAccessOnly) {\n        const linkData = event.subject.data;\n        if (linkData) {\n          if (linkData.category == GojsNodeCategory.LinkToLink) {\n            this.goJsCardinalityService.updatePortForLinkToLink(linkData);\n          } else {\n            this.goJsCardinalityService.handleReDrawnLink(linkData, this.diagram);\n            event.diagram.clearSelection();\n          }\n        }\n      }\n    });\n  }\n  /**\n   * Adds a listener for the 'ModelChanged' event on the diagram.\n   * @private\n   */\n  addModelChangedListener() {\n    this.diagram.addModelChangedListener(event => {\n      if (event.isTransactionFinished) {\n        const action = event.propertyName === 'FinishedUndo' ? 'undo' : event.propertyName === 'FinishedRedo' ? 'redo' : null;\n        if (action) {\n          this.handleUndoRedo(event, action);\n        }\n        const isLabelShifted = event.propertyName === 'CommittedTransaction' && event.oldValue === 'Shifted Label';\n        if (isLabelShifted) {\n          if (event.object) {\n            let segmentOffset;\n            let linkData;\n            event.object['changes']['iterator'].each(change => {\n              if (change['propertyName'] === 'segmentOffset') {\n                segmentOffset = change['newValue'];\n                linkData = change['object'];\n              }\n            });\n            this.goJsCardinalityService.updateLinkOnTextEdited(linkData);\n          }\n        }\n      }\n    });\n  }\n  /**\n   * Handles undo and redo operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleUndoRedo(event, action) {\n    if (event.object) {\n      if (event.object['name'] == 'Delete') {\n        this.handleDelete(event, action);\n      } else if (event.object['name'] == 'ExternalCopy') {\n        this.handleExternalCopy(event, action);\n      } else if (event.object['name'] == 'Linking') {\n        this.goJsCardinalityService.handleUndoRedoLinking(event, action, false);\n      } else if (event.object['name'] == 'Move') {\n        this.handleMove(event, action);\n      } else if (event.object['name'] == 'Resizing') {\n        this.handleResizing(event, action);\n      } else if (event.object['name'] == 'TextEditing') {\n        this.handleTextEditingUndoRedo(event, action);\n      } else if (event.object['name'] == 'Layout') {\n        this.handleLayoutChanges(event, action);\n      } else if (event.object['name'] == '') {\n        this.handleDelete(event, action);\n      }\n    }\n  }\n  handleTextEditingUndoRedo(event, action) {\n    if (!event.object) return;\n    const changesIterator = event.object['changes']['iterator'];\n    changesIterator.each(change => {\n      if (change['propertyName'] === 'text') {\n        this.handleTextChange(change, action);\n      } else if (change['propertyName'] === 'description') {\n        this.handleDescriptionChange(change, action);\n      }\n    });\n  }\n  handleTextChange(change, action) {\n    const modifiedName = action === 'undo' ? change['oldValue'] : change['newValue'];\n    const nodeValue = this.getSelectedNode(change);\n    if (!nodeValue) return;\n    switch (nodeValue.data.category) {\n      case GojsNodeCategory.Comment:\n        this.gojsCommentService.updateComment(modifiedName, nodeValue.data.description, nodeValue.data, this.diagram);\n        break;\n      case GojsNodeCategory.Association:\n        this.goJsCardinalityService.updateLinkOnTextEdited({\n          ...nodeValue.data\n        });\n        break;\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.gojsClassService.updateTemplateClass(nodeValue.data, this.diagram);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.goJsEnumerationService.updateEnumerationFromDiagram(nodeValue.data, this.diagram);\n        break;\n    }\n  }\n  handleDescriptionChange(change, action) {\n    const modifiedDescription = action === 'undo' ? change['oldValue'] : change['newValue'];\n    const nodeValue = this.getSelectedNode(change);\n    if (nodeValue?.data.category === GojsNodeCategory.Comment) {\n      this.gojsCommentService.updateComment(nodeValue.data.name, modifiedDescription, nodeValue.data, this.diagram);\n    }\n  }\n  getSelectedNode(change) {\n    // First try to get the node from the change's diagram selection\n    const diagramFromChange = change['diagram'];\n    if (diagramFromChange?.selection?.first) {\n      return diagramFromChange.selection.first() || null;\n    }\n    // If that fails, try to get it from the current diagram's selection\n    if (this.diagram?.selection?.first) {\n      return this.diagram.selection.first() || null;\n    }\n    // If we have an object in the change, try to find the corresponding node\n    if (change['object'] && change['object'].key && this.diagram) {\n      const key = change['object'].key;\n      return this.diagram.findNodeForKey(key) || null;\n    }\n    // As a last resort, if we have data in the change, try to find the node by data\n    if (change['object'] && this.diagram) {\n      return null; // Return null for now, but we could implement additional lookup logic if needed\n    }\n    return null;\n  }\n  /**\n   * Handles delete operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleDelete(event, action) {\n    if (!event.object) return;\n    const changesIterator = event.object['changes']['iterator'];\n    changesIterator.each(change => {\n      if (change['propertyName'] === 'nodeDataArray') {\n        this.handleNodeDeletion(change['oldValue'], action);\n      } else if (change['propertyName'] === 'linkDataArray') {\n        this.handleLinkDeletion(event, action);\n      }\n    });\n  }\n  handleNodeDeletion(nodeData, action) {\n    if (!nodeData) return;\n    const nodeId = nodeData.id;\n    const category = nodeData.category;\n    if (action === 'undo') {\n      this.undoNodeDeletion(category, nodeId);\n    } else {\n      this.performNodeDeletion(category, nodeId);\n    }\n  }\n  undoNodeDeletion(category, nodeId) {\n    switch (category) {\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.classService.undoClassDeletion(nodeId);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.enumerationService.undoEnumDeletion(nodeId);\n        break;\n      case GojsNodeCategory.Comment:\n        this.commentService.undoCommentDeletion(nodeId);\n        break;\n    }\n  }\n  performNodeDeletion(category, nodeId) {\n    switch (category) {\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.classService.deleteClasses([nodeId]);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.enumerationService.deleteEnumerations([nodeId]);\n        break;\n      case GojsNodeCategory.Comment:\n        this.commentService.deleteComment([nodeId]);\n        break;\n    }\n  }\n  handleLinkDeletion(event, action) {\n    this.goJsCardinalityService.handleUndoRedoLinking(event, action === 'undo' ? 'redo' : 'undo', true);\n  }\n  /**\n   * Handles external copy operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleExternalCopy(event, action) {\n    if (!event.object) return;\n    const changesIterator = event.object['changes']['iterator'];\n    changesIterator.each(change => {\n      if (change['propertyName'] === 'nodeDataArray') {\n        const nodeData = change['newValue'];\n        if (!nodeData) return;\n        if (action === 'redo') {\n          this.restoreNodeData(nodeData);\n        } else {\n          this.deleteNodeData(nodeData);\n          this.diagram.model.updateTargetBindings(nodeData);\n        }\n      }\n    });\n  }\n  restoreNodeData(nodeData) {\n    switch (nodeData.category) {\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.classService.undoClassDeletion(nodeData.id);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.enumerationService.undoEnumDeletion(nodeData.id);\n        break;\n      case GojsNodeCategory.Comment:\n        this.commentService.undoCommentDeletion(nodeData.id);\n        break;\n    }\n  }\n  deleteNodeData(nodeData) {\n    switch (nodeData.category) {\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.classService.deleteClasses([nodeData.id]);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.enumerationService.deleteTemplateEnums([nodeData.id]);\n        break;\n      case GojsNodeCategory.Comment:\n        this.commentService.deleteComment([nodeData.id]);\n        break;\n    }\n  }\n  /**\n   * Handles move operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleMove(event, action) {\n    if (event.object) {\n      const nodeData = event.object['changes']['iterator'].filter(obj => obj['propertyName'] == 'position' && !obj['object'].data).first();\n      if (nodeData) {\n        if (nodeData.object.category == GojsNodeCategory.Class || nodeData.object.category == GojsNodeCategory.AssociativeClass) {\n          this.classService.updateClass({\n            id: nodeData.object.id,\n            idDiagram: this.currentDiagram.id,\n            idTemplateClass: nodeData.object.idTemplateClass,\n            key: nodeData.object.key,\n            name: nodeData.object.name,\n            description: nodeData.object.description,\n            volumetry: nodeData.object.volumetry,\n            tag: nodeData.object.tag,\n            isAssociative: nodeData.object.category == GojsNodeCategory.AssociativeClass ? true : false,\n            property: {\n              color: nodeData.object.color,\n              height: nodeData.object.size.height,\n              width: nodeData.object.size.width,\n              icon: nodeData.object.icon,\n              position: action === 'undo' ? nodeData.oldValue : nodeData.newValue\n            }\n          });\n        } else if (nodeData.object.category == GojsNodeCategory.Enumeration) {\n          this.enumerationService.updateEnumeration({\n            id: nodeData.object.id,\n            idDiagram: this.currentDiagram.id,\n            key: nodeData.object.key,\n            name: nodeData.object.name,\n            description: nodeData.object.description,\n            volumetry: nodeData.object.volumetry,\n            tag: nodeData.object.tag,\n            idTemplateEnumeration: nodeData.object.idTemplateEnumeration,\n            property: {\n              color: nodeData.object.color,\n              height: nodeData.object.size.height,\n              width: nodeData.object.size.width,\n              icon: nodeData.object.icon,\n              position: action === 'undo' ? nodeData.oldValue : nodeData.newValue\n            }\n          });\n        } else if (nodeData.object.category == GojsNodeCategory.Comment) {\n          this.commentService.updateExistingComment({\n            id: nodeData.object.id,\n            name: nodeData.object.name,\n            description: nodeData.object.description,\n            height: nodeData.object.size.height,\n            width: nodeData.object.size.width,\n            position: action === 'undo' ? nodeData.oldValue : nodeData.newValue\n          });\n        }\n      }\n    }\n  }\n  /**\n   * Handles resizing operations for the diagram.\n   * @private\n   * @param {go.ChangedEvent} event - The change event.\n   * @param {string} action - The action type ('undo' or 'redo').\n   * @memberof DiagramEditorComponent\n   */\n  handleResizing(event, action) {\n    if (event.object) {\n      const nodeData = event.object['changes']['iterator'].filter(obj => obj['propertyName'] == 'desiredSize' && !obj['object'].data)[0];\n      if (nodeData) {\n        this.classService.updateClass({\n          id: nodeData.object.id,\n          idDiagram: this.currentDiagram.id,\n          idTemplateClass: nodeData.object.idTemplateClass,\n          key: nodeData.object.key,\n          name: nodeData.object.name,\n          isAssociative: nodeData.object.category == GojsNodeCategory.AssociativeClass ? true : false,\n          property: {\n            color: nodeData.object.color,\n            height: action === 'undo' ? nodeData.oldValue.height : nodeData.newValue.height,\n            width: action === 'undo' ? nodeData.oldValue.width : nodeData.newValue.width,\n            icon: nodeData.object.icon,\n            position: nodeData.object.position\n          }\n        });\n      }\n    }\n  }\n  handleLayoutChanges(event, action) {\n    if (event.object) {\n      let attributes = [];\n      event.object['changes']['iterator'].each(obj => {\n        if (obj['propertyName'] == 'itemArray') {\n          attributes.push(...obj['oldValue']);\n        }\n      });\n      attributes = Array.from(new Set(attributes.map(item => JSON.stringify(item)))).map(item => JSON.parse(item));\n      this.diagram.model.nodeDataArray.forEach(node => {});\n    }\n  }\n  static #_ = this.ɵfac = function EventListenerService_Factory(t) {\n    return new (t || EventListenerService)(i0.ɵɵinject(i1.AccessService), i0.ɵɵinject(i2.ClassService), i0.ɵɵinject(i3.GojsCardinalityService), i0.ɵɵinject(i4.PropertyService), i0.ɵɵinject(i5.DiagramUtils), i0.ɵɵinject(i6.GojsCommonService), i0.ɵɵinject(i7.GojsEnumerationService), i0.ɵɵinject(i8.EnumerationService), i0.ɵɵinject(i9.GojsCommentService), i0.ɵɵinject(i10.GojsAttributeService), i0.ɵɵinject(i11.GojsClassService), i0.ɵɵinject(i12.GojsLiteralService), i0.ɵɵinject(i13.CommentService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: EventListenerService,\n    factory: EventListenerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["go", "GojsNodeCategory", "AccessType", "EventListenerService", "constructor", "accessService", "classService", "goJsCardinalityService", "propertyService", "diagramUtils", "commonGojsService", "goJsEnumerationService", "enumerationService", "gojsCommentService", "goJsAttrService", "gojsClassService", "goJsLiteralService", "commentService", "addDeletingEventListener", "event", "hasEditAccessOnly", "nodes", "subject", "classIds", "enumIds", "hasLinks", "each", "node", "data", "category", "Association", "cancel", "handleLinkDeletion", "handleNonLinkDeletion", "accessTypeChanges", "subscribe", "response", "Viewer", "activeDiagramChanges", "diagram", "currentDiagram", "gojsDiagramChanges", "addDiagramEventListeners", "addSelectionDeletingListener", "addPartResizedListener", "addTextEditedListener", "addLinkDrawnListener", "addLinkReDrawnListener", "addModelChangedListener", "addDiagramListener", "deleteLink", "count", "Class", "AssociativeClass", "push", "id", "Enumeration", "Comment", "handleCommentDelete", "length", "deleteClasses", "deleteEnumerations", "setPropertyData", "e", "handlePartResized", "shape", "diagramData", "selection", "first", "size", "Size", "actualBounds", "width", "height", "updateClass", "updateExistingEnum", "updateComment", "name", "description", "idDiagram", "key", "isAssociative", "property", "position", "icon", "color", "panel", "handleTextEditedListener", "updateLinkOnTextEdited", "Attribute", "Operation", "updateAttributeOnTextEdited", "EnumerationLiteral", "handleEditLiteralNameInDiagram", "handleLinkDrawn", "linkData", "toNode", "getObjectDataByKey", "to", "fromNode", "from", "LinkLabel", "linkToLinkObj", "createLinkToLinkObject", "createNewLinkToLinkAndUpdate", "deletedLink", "findDeletedLink", "newLink", "createLinkObject", "handleDeletedLink", "createNewLinkAndUpdate", "clearSelection", "getDeletedLinks", "find", "link", "idSourceClass", "idDestinationClass", "idSourceTempClass", "idDestinationTempClass", "idLinkType", "sourcePort", "fromPort", "destinationPort", "to<PERSON><PERSON>", "fromComment", "toComment", "segmentOffset", "linkPort", "idLink", "idAssociativeClass", "port", "LinkToLink", "updatePortForLinkToLink", "handleReDrawnLink", "isTransactionFinished", "action", "propertyName", "handleUndoRedo", "isLabelShifted", "oldValue", "object", "change", "handleDelete", "handleExternalCopy", "handleUndoRedoLinking", "handleMove", "handleResizing", "handleTextEditingUndoRedo", "handleLayoutChanges", "changesIterator", "handleTextChange", "handleDescriptionChange", "modifiedName", "nodeValue", "getSelectedNode", "updateTemplateClass", "updateEnumerationFromDiagram", "modifiedDescription", "diagramFromChange", "findNodeForKey", "handleNodeDeletion", "nodeData", "nodeId", "undoNodeDeletion", "performNodeDeletion", "undoClassDeletion", "undoEnumDeletion", "undoCommentDeletion", "deleteComment", "restoreNodeData", "deleteNodeData", "model", "updateTargetBindings", "deleteTemplateEnums", "filter", "obj", "idTemplateClass", "volumetry", "tag", "newValue", "updateEnumeration", "idTemplateEnumeration", "updateExistingComment", "attributes", "Array", "Set", "map", "item", "JSON", "stringify", "parse", "nodeDataArray", "for<PERSON>ach", "_", "i0", "ɵɵinject", "i1", "AccessService", "i2", "ClassService", "i3", "GojsCardinalityService", "i4", "PropertyService", "i5", "DiagramUtils", "i6", "GojsCommonService", "i7", "GojsEnumerationService", "i8", "EnumerationService", "i9", "GojsCommentService", "i10", "GojsAttributeService", "i11", "GojsClassService", "i12", "GojsLiteralService", "i13", "CommentService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitCodeBassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\diagram\\event-listener.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport go from 'gojs';\r\nimport {\r\n  CardinalityCreate,\r\n  DeletedLink,\r\n  LinkToLink,\r\n} from 'src/app/shared/model/cardinality';\r\nimport { CommentPatch } from 'src/app/shared/model/comment';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { Enumeration } from 'src/app/shared/model/enumeration';\r\nimport {\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsLinkNode,\r\n  GoJsLinkToLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { GojsDiagramAttributeNode } from '../../../shared/model/gojs';\r\nimport { AccessService } from '../access/access.service';\r\nimport { ClassService } from '../class/class.service';\r\nimport { CommentService } from '../comment/comment.service';\r\nimport { EnumerationService } from '../enumeration/enumeration.service';\r\nimport { GojsCommentService } from '../gojs/gojs-comment/gojs-comment.service';\r\nimport { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';\r\nimport { GojsCardinalityService } from '../gojs/gojsCardinality/gojs-cardinality.service';\r\nimport { GojsClassService } from '../gojs/gojsClass/gojs-class.service';\r\nimport { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';\r\nimport { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';\r\nimport { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';\r\nimport { PropertyService } from '../property/property.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class EventListenerService {\r\n  private hasEditAccessOnly!: boolean;\r\n  private diagram!: go.Diagram;\r\n  private currentDiagram!: Diagram;\r\n  constructor(\r\n    private accessService: AccessService,\r\n    private classService: ClassService,\r\n    private goJsCardinalityService: GojsCardinalityService,\r\n    private propertyService: PropertyService,\r\n    private diagramUtils: DiagramUtils,\r\n    private commonGojsService: GojsCommonService,\r\n    private goJsEnumerationService: GojsEnumerationService,\r\n    private enumerationService: EnumerationService,\r\n    private gojsCommentService: GojsCommentService,\r\n    private goJsAttrService: GojsAttributeService,\r\n    private gojsClassService: GojsClassService,\r\n    private goJsLiteralService: GojsLiteralService,\r\n    private commentService: CommentService\r\n  ) {\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      if (response != AccessType.Viewer) {\r\n        this.hasEditAccessOnly = true;\r\n      } else {\r\n        this.hasEditAccessOnly = false;\r\n      }\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n\r\n    this.commonGojsService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.diagram = diagram;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Adds event listeners to the diagram for handling model changes and other interactions.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  addDiagramEventListeners(): void {\r\n    this.addSelectionDeletingListener();\r\n    this.addPartResizedListener();\r\n    this.addTextEditedListener();\r\n    this.addLinkDrawnListener();\r\n    this.addLinkReDrawnListener();\r\n    this.addModelChangedListener();\r\n  }\r\n\r\n  /**\r\n   * Adds a listener for the 'SelectionDeleting' event on the diagram.\r\n   * @private\r\n   */\r\n  private addSelectionDeletingListener(): void {\r\n    this.diagram.addDiagramListener(\r\n      'SelectionDeleting',\r\n      this.addDeletingEventListener\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Selection delete event listener for deleting the selective class\r\n   * @private\r\n   * @param {go.DiagramEvent} event\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  addDeletingEventListener = (event: go.DiagramEvent) => {\r\n    if (this.hasEditAccessOnly) {\r\n      const nodes = event.subject;\r\n      const classIds: number[] = [],\r\n        enumIds: number[] = [];\r\n\r\n      // Check if any of the selected nodes are links (associations)\r\n      let hasLinks = false;\r\n      nodes.each((node: go.Node) => {\r\n        if (node.data.category == GojsNodeCategory.Association) {\r\n          hasLinks = true;\r\n        }\r\n      });\r\n\r\n      // Cancel the event initially\r\n      event.cancel = true;\r\n\r\n      // If there are links, handle them with confirmation\r\n      if (hasLinks) {\r\n        this.handleLinkDeletion(nodes, event);\r\n      } else {\r\n        // For non-link nodes, proceed with immediate deletion\r\n        this.handleNonLinkDeletion(nodes, classIds, enumIds);\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handles deletion of link nodes with confirmation\r\n   * @private\r\n   * @param {go.Set<go.Node>} nodes - The nodes to be deleted\r\n   * @param {go.DiagramEvent} event - The diagram event\r\n   */\r\n  private handleLinkDeletion(\r\n    nodes: go.Set<go.Node>,\r\n    event: go.DiagramEvent\r\n  ): void {\r\n    nodes.each((node: go.Node) => {\r\n      if (node.data.category == GojsNodeCategory.Association) {\r\n        // Call deleteLink which will handle the confirmation dialog\r\n        this.goJsCardinalityService.deleteLink(\r\n          node,\r\n          nodes.count,\r\n          this.diagram,\r\n          event\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles deletion of non-link nodes (classes, enums, comments)\r\n   * @private\r\n   * @param {go.Set<go.Node>} nodes - The nodes to be deleted\r\n   * @param {number[]} classIds - Array to collect class IDs\r\n   * @param {number[]} enumIds - Array to collect enum IDs\r\n   */\r\n  private handleNonLinkDeletion(\r\n    nodes: go.Set<go.Node>,\r\n    classIds: number[],\r\n    enumIds: number[]\r\n  ): void {\r\n    nodes.each((node: go.Node) => {\r\n      if (\r\n        node.data.category == GojsNodeCategory.Class ||\r\n        node.data.category == GojsNodeCategory.AssociativeClass\r\n      ) {\r\n        classIds.push(node.data.id);\r\n      } else if (node.data.category == GojsNodeCategory.Enumeration) {\r\n        enumIds.push(node.data.id);\r\n      } else if (node.data.category == GojsNodeCategory.Comment) {\r\n        this.gojsCommentService.handleCommentDelete([node.data.id]);\r\n      }\r\n    });\r\n\r\n    if (classIds.length > 0) this.classService.deleteClasses(classIds);\r\n    if (enumIds.length > 0) this.enumerationService.deleteEnumerations(enumIds);\r\n    this.propertyService.setPropertyData(null);\r\n  }\r\n\r\n  /**\r\n   * Adds a listener for the 'PartResized' event on the diagram.\r\n   * @private\r\n   */\r\n  private addPartResizedListener(): void {\r\n    this.diagram.addDiagramListener('PartResized', (e) => {\r\n      if (this.hasEditAccessOnly) {\r\n        this.handlePartResized(e);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles the 'PartResized' event on the diagram.\r\n   * @private\r\n   * @param {go.DiagramEvent} event - The event object.\r\n   */\r\n  private handlePartResized(event: go.DiagramEvent): void {\r\n    const shape = event.subject as go.Shape;\r\n    let diagramData = event.diagram.selection.first()?.data;\r\n    diagramData.size = new go.Size(\r\n      shape.actualBounds.width,\r\n      shape.actualBounds.height\r\n    );\r\n    if (\r\n      diagramData.category === GojsNodeCategory.Class ||\r\n      diagramData.category === GojsNodeCategory.AssociativeClass\r\n    ) {\r\n      this.updateClass(diagramData);\r\n    } else if (diagramData.category === GojsNodeCategory.Enumeration) {\r\n      this.goJsEnumerationService.updateExistingEnum(\r\n        diagramData as GojsDiagramEnumerationNode,\r\n        false\r\n      );\r\n    } else if (diagramData.category === GojsNodeCategory.Comment) {\r\n      this.gojsCommentService.updateComment(\r\n        diagramData.name,\r\n        diagramData.description,\r\n        diagramData,\r\n        event.diagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the class based on the resized data.\r\n   * @private\r\n   * @param {GojsDiagramClassNode} diagramData - The data of the resized diagram element.\r\n   */\r\n  private updateClass(diagramData: GojsDiagramClassNode): void {\r\n    this.classService.updateClass({\r\n      id: diagramData.id,\r\n      name: diagramData.name,\r\n      idDiagram: this.currentDiagram.id!,\r\n      key: diagramData.key,\r\n      isAssociative: diagramData.isAssociative,\r\n      property: {\r\n        position: diagramData.position!,\r\n        height: diagramData.size.height,\r\n        width: diagramData.size.width,\r\n        icon: diagramData.icon,\r\n        color: diagramData.color,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Adds a listener for the 'TextEdited' event on the diagram.\r\n   * @private\r\n   */\r\n  private addTextEditedListener(): void {\r\n    this.diagram.addDiagramListener('TextEdited', (e) => {\r\n      if (\r\n        e.subject.panel &&\r\n        (e.subject.panel.data ||\r\n          e.diagram.selection.first()?.data.category ===\r\n            GojsNodeCategory.Association) &&\r\n        this.hasEditAccessOnly\r\n      ) {\r\n        this.handleTextEditedListener(e);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles the editing of text elements on the diagram.\r\n   * Updates links or attributes based on the edited data.\r\n   * @private\r\n   * @param {go.DiagramEvent} event - The event object.\r\n   */\r\n  private handleTextEditedListener(event: go.DiagramEvent): void {\r\n    if (\r\n      event.diagram.selection &&\r\n      event.diagram.selection.first()?.data &&\r\n      event.diagram.selection.first()?.data.category ==\r\n        GojsNodeCategory.Association\r\n    ) {\r\n      this.goJsCardinalityService.updateLinkOnTextEdited(\r\n        event.diagram.selection.first()?.data\r\n      );\r\n    } else if (\r\n      event.subject.panel.data.category == GojsNodeCategory.Attribute ||\r\n      event.subject.panel.data.category == GojsNodeCategory.Operation\r\n    ) {\r\n      this.goJsAttrService.updateAttributeOnTextEdited(event, this.diagram);\r\n    } else if (\r\n      event.subject.panel.data.category == GojsNodeCategory.EnumerationLiteral\r\n    ) {\r\n      this.goJsLiteralService.handleEditLiteralNameInDiagram(\r\n        event,\r\n        this.diagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds a listener for the 'LinkDrawn' event on the diagram.\r\n   * @private\r\n   */\r\n  private addLinkDrawnListener(): void {\r\n    this.diagram.addDiagramListener('LinkDrawn', (event) => {\r\n      if (this.hasEditAccessOnly) {\r\n        this.handleLinkDrawn(event);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles the event when a link is drawn on the diagram.\r\n   * Checks if the link is between valid nodes and creates or updates the link accordingly.\r\n   * @private\r\n   * @param {go.DiagramEvent} event - The event object.\r\n   */\r\n  private handleLinkDrawn(event: go.DiagramEvent): void {\r\n    const linkData: GojsLinkNode | GoJsLinkToLinkNode = event.subject.data;\r\n    const toNode = this.diagramUtils.getObjectDataByKey(\r\n      this.diagram,\r\n      linkData.to\r\n    );\r\n    const fromNode = this.diagramUtils.getObjectDataByKey(\r\n      this.diagram,\r\n      linkData.from\r\n    );\r\n    if (toNode && fromNode) {\r\n      if (\r\n        fromNode['category'] == GojsNodeCategory.AssociativeClass &&\r\n        toNode['category'] == GojsNodeCategory.LinkLabel\r\n      ) {\r\n        const linkToLinkObj = this.createLinkToLinkObject(\r\n          fromNode,\r\n          toNode,\r\n          linkData as GoJsLinkToLinkNode\r\n        );\r\n        this.goJsCardinalityService.createNewLinkToLinkAndUpdate(\r\n          linkData as GoJsLinkToLinkNode,\r\n          linkToLinkObj,\r\n          this.diagram\r\n        );\r\n      } else {\r\n        const deletedLink = this.findDeletedLink(fromNode, toNode);\r\n        const newLink = this.createLinkObject(\r\n          fromNode,\r\n          toNode,\r\n          linkData as GojsLinkNode\r\n        );\r\n        if (deletedLink) {\r\n          this.goJsCardinalityService.handleDeletedLink(\r\n            deletedLink,\r\n            linkData as GojsLinkNode,\r\n            newLink,\r\n            fromNode,\r\n            toNode,\r\n            this.diagram\r\n          );\r\n        } else {\r\n          this.goJsCardinalityService.createNewLinkAndUpdate(\r\n            linkData as GojsLinkNode,\r\n            newLink,\r\n            fromNode,\r\n            toNode,\r\n            this.diagram\r\n          );\r\n          event.diagram.clearSelection();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Finds a deleted link between two nodes.\r\n   * @private\r\n   * @param {go.ObjectData} fromNode - The source node.\r\n   * @param {go.ObjectData} toNode - The destination node.\r\n   * @returns {DeletedLink | undefined } The deleted link if found, otherwise null.\r\n   */\r\n  private findDeletedLink(\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData\r\n  ): DeletedLink | undefined {\r\n    return this.diagramUtils\r\n      .getDeletedLinks()\r\n      .find(\r\n        (link) =>\r\n          (link.idSourceClass == fromNode['id'] &&\r\n            link.idDestinationClass == toNode['id']) ||\r\n          (link.idSourceClass == toNode['id'] &&\r\n            link.idDestinationClass == fromNode['id'])\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Creates a new link object based on the source and destination nodes.\r\n   * @private\r\n   * @param {go.ObjectData} fromNode - The source node.\r\n   * @param {go.ObjectData} toNode - The destination node.\r\n   * @param {GojsLinkNode} linkData - The link data.\r\n   * @returns {CardinalityDetails} The new link object.\r\n   */\r\n  private createLinkObject(\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData,\r\n    linkData: GojsLinkNode\r\n  ): CardinalityCreate {\r\n    return {\r\n      name: `${fromNode['name']} - ${toNode['name']}`,\r\n      id: linkData.id,\r\n      idSourceTempClass: fromNode['idTemplateClass'],\r\n      idDestinationTempClass: toNode['idTemplateClass'],\r\n      idLinkType: 1,\r\n      sourcePort: linkData.fromPort!,\r\n      destinationPort: linkData.toPort!,\r\n      idDiagram: this.currentDiagram.id!,\r\n      color: 'rgba(0, 0, 0, 1)',\r\n      fromComment: linkData.fromComment,\r\n      toComment: linkData.toComment,\r\n      segmentOffset: '0 0',\r\n      linkPort: {\r\n        idDiagram: this.currentDiagram.id!,\r\n        destinationPort: linkData.toPort!,\r\n        sourcePort: linkData.fromPort!,\r\n        idLink: linkData.id!,\r\n        segmentOffset: '0 0',\r\n      },\r\n    };\r\n  }\r\n  private createLinkToLinkObject(\r\n    fromNode: go.ObjectData,\r\n    toNode: go.ObjectData,\r\n    linkData: GoJsLinkToLinkNode\r\n  ): LinkToLink {\r\n    return {\r\n      idLink: toNode['idLink'],\r\n      idAssociativeClass: fromNode['idTemplateClass'],\r\n      port: linkData.fromPort!,\r\n    };\r\n  }\r\n\r\n  private addLinkReDrawnListener() {\r\n    this.diagram.addDiagramListener('LinkRelinked', (event) => {\r\n      if (this.hasEditAccessOnly) {\r\n        const linkData = event.subject.data;\r\n        if (linkData) {\r\n          if (linkData.category == GojsNodeCategory.LinkToLink) {\r\n            this.goJsCardinalityService.updatePortForLinkToLink(linkData);\r\n          } else {\r\n            this.goJsCardinalityService.handleReDrawnLink(\r\n              linkData,\r\n              this.diagram\r\n            );\r\n            event.diagram.clearSelection();\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Adds a listener for the 'ModelChanged' event on the diagram.\r\n   * @private\r\n   */\r\n  private addModelChangedListener(): void {\r\n    this.diagram.addModelChangedListener((event) => {\r\n      if (event.isTransactionFinished) {\r\n        const action =\r\n          event.propertyName === 'FinishedUndo'\r\n            ? 'undo'\r\n            : event.propertyName === 'FinishedRedo'\r\n            ? 'redo'\r\n            : null;\r\n        if (action) {\r\n          this.handleUndoRedo(event, action);\r\n        }\r\n\r\n        const isLabelShifted =\r\n          event.propertyName === 'CommittedTransaction' &&\r\n          event.oldValue === 'Shifted Label';\r\n        if (isLabelShifted) {\r\n          if (event.object) {\r\n            let segmentOffset;\r\n            let linkData;\r\n            event.object['changes']['iterator'].each(\r\n              (change: go.ObjectData) => {\r\n                if (change['propertyName'] === 'segmentOffset') {\r\n                  segmentOffset = change['newValue'];\r\n                  linkData = change['object'];\r\n                }\r\n              }\r\n            );\r\n            this.goJsCardinalityService.updateLinkOnTextEdited(\r\n              linkData! as GojsLinkNode\r\n            );\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles undo and redo operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private handleUndoRedo(event: go.ChangedEvent, action: string): void {\r\n    if (event.object) {\r\n      if (event.object['name'] == 'Delete') {\r\n        this.handleDelete(event, action);\r\n      } else if (event.object['name'] == 'ExternalCopy') {\r\n        this.handleExternalCopy(event, action);\r\n      } else if (event.object['name'] == 'Linking') {\r\n        this.goJsCardinalityService.handleUndoRedoLinking(event, action, false);\r\n      } else if (event.object['name'] == 'Move') {\r\n        this.handleMove(event, action);\r\n      } else if (event.object['name'] == 'Resizing') {\r\n        this.handleResizing(event, action);\r\n      } else if (event.object['name'] == 'TextEditing') {\r\n        this.handleTextEditingUndoRedo(event, action);\r\n      } else if (event.object['name'] == 'Layout') {\r\n        this.handleLayoutChanges(event, action);\r\n      } else if (event.object['name'] == '') {\r\n        this.handleDelete(event, action);\r\n      }\r\n    }\r\n  }\r\n\r\n  private handleTextEditingUndoRedo(event: go.ChangedEvent, action: string) {\r\n    if (!event.object) return;\r\n\r\n    const changesIterator = event.object['changes']['iterator'];\r\n\r\n    changesIterator.each((change: go.ObjectData) => {\r\n      if (change['propertyName'] === 'text') {\r\n        this.handleTextChange(change, action);\r\n      } else if (change['propertyName'] === 'description') {\r\n        this.handleDescriptionChange(change, action);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleTextChange(change: go.ObjectData, action: string) {\r\n    const modifiedName =\r\n      action === 'undo' ? change['oldValue'] : change['newValue'];\r\n\r\n    const nodeValue = this.getSelectedNode(change);\r\n    if (!nodeValue) return;\r\n\r\n    switch (nodeValue.data.category) {\r\n      case GojsNodeCategory.Comment:\r\n        this.gojsCommentService.updateComment(\r\n          modifiedName,\r\n          nodeValue.data.description,\r\n          nodeValue.data,\r\n          this.diagram\r\n        );\r\n        break;\r\n      case GojsNodeCategory.Association:\r\n        this.goJsCardinalityService.updateLinkOnTextEdited({\r\n          ...nodeValue.data,\r\n        });\r\n        break;\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.gojsClassService.updateTemplateClass(nodeValue.data, this.diagram);\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.goJsEnumerationService.updateEnumerationFromDiagram(\r\n          nodeValue.data,\r\n          this.diagram\r\n        );\r\n        break;\r\n    }\r\n  }\r\n\r\n  private handleDescriptionChange(change: go.ObjectData, action: string) {\r\n    const modifiedDescription =\r\n      action === 'undo' ? change['oldValue'] : change['newValue'];\r\n\r\n    const nodeValue = this.getSelectedNode(change);\r\n    if (nodeValue?.data.category === GojsNodeCategory.Comment) {\r\n      this.gojsCommentService.updateComment(\r\n        nodeValue.data.name,\r\n        modifiedDescription,\r\n        nodeValue.data,\r\n        this.diagram\r\n      );\r\n    }\r\n  }\r\n\r\n  private getSelectedNode(change: go.ObjectData): go.Part | null {\r\n    // First try to get the node from the change's diagram selection\r\n    const diagramFromChange = change['diagram'];\r\n    if (diagramFromChange?.selection?.first) {\r\n      return diagramFromChange.selection.first() || null;\r\n    }\r\n\r\n    // If that fails, try to get it from the current diagram's selection\r\n    if (this.diagram?.selection?.first) {\r\n      return this.diagram.selection.first() || null;\r\n    }\r\n\r\n    // If we have an object in the change, try to find the corresponding node\r\n    if (change['object'] && change['object'].key && this.diagram) {\r\n      const key = change['object'].key;\r\n      return this.diagram.findNodeForKey(key) || null;\r\n    }\r\n\r\n    // As a last resort, if we have data in the change, try to find the node by data\r\n    if (change['object'] && this.diagram) {\r\n      return null; // Return null for now, but we could implement additional lookup logic if needed\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Handles delete operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private handleDelete(event: go.ChangedEvent, action: string): void {\r\n    if (!event.object) return;\r\n    const changesIterator = event.object['changes']['iterator'];\r\n    changesIterator.each((change: go.ObjectData) => {\r\n      if (change['propertyName'] === 'nodeDataArray') {\r\n        this.handleNodeDeletion(change['oldValue'], action);\r\n      } else if (change['propertyName'] === 'linkDataArray') {\r\n        this.handleLinkDeletion(event, action);\r\n      }\r\n    });\r\n  }\r\n\r\n  private handleNodeDeletion(nodeData: any, action: string): void {\r\n    if (!nodeData) return;\r\n    const nodeId = nodeData.id;\r\n    const category = nodeData.category;\r\n    if (action === 'undo') {\r\n      this.undoNodeDeletion(category, nodeId);\r\n    } else {\r\n      this.performNodeDeletion(category, nodeId);\r\n    }\r\n  }\r\n\r\n  private undoNodeDeletion(category: GojsNodeCategory, nodeId: number): void {\r\n    switch (category) {\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.classService.undoClassDeletion(nodeId);\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.enumerationService.undoEnumDeletion(nodeId);\r\n        break;\r\n      case GojsNodeCategory.Comment:\r\n        this.commentService.undoCommentDeletion(nodeId);\r\n        break;\r\n    }\r\n  }\r\n\r\n  private performNodeDeletion(\r\n    category: GojsNodeCategory,\r\n    nodeId: number\r\n  ): void {\r\n    switch (category) {\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.classService.deleteClasses([nodeId]);\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.enumerationService.deleteEnumerations([nodeId]);\r\n        break;\r\n      case GojsNodeCategory.Comment:\r\n        this.commentService.deleteComment([nodeId]);\r\n        break;\r\n    }\r\n  }\r\n\r\n  private handleLinkDeletion(event: go.ChangedEvent, action: string): void {\r\n    this.goJsCardinalityService.handleUndoRedoLinking(\r\n      event,\r\n      action === 'undo' ? 'redo' : 'undo',\r\n      true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Handles external copy operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private handleExternalCopy(event: go.ChangedEvent, action: string): void {\r\n    if (!event.object) return;\r\n    const changesIterator = event.object['changes']['iterator'];\r\n    changesIterator.each((change: go.ObjectData) => {\r\n      if (change['propertyName'] === 'nodeDataArray') {\r\n        const nodeData = change['newValue'];\r\n        if (!nodeData) return;\r\n\r\n        if (action === 'redo') {\r\n          this.restoreNodeData(nodeData);\r\n        } else {\r\n          this.deleteNodeData(nodeData);\r\n          this.diagram.model.updateTargetBindings(nodeData);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private restoreNodeData(nodeData: any): void {\r\n    switch (nodeData.category) {\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.classService.undoClassDeletion(nodeData.id);\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.enumerationService.undoEnumDeletion(nodeData.id);\r\n        break;\r\n      case GojsNodeCategory.Comment:\r\n        this.commentService.undoCommentDeletion(nodeData.id);\r\n        break;\r\n    }\r\n  }\r\n\r\n  private deleteNodeData(nodeData: any): void {\r\n    switch (nodeData.category) {\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.classService.deleteClasses([nodeData.id]);\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.enumerationService.deleteTemplateEnums([nodeData.id]);\r\n        break;\r\n      case GojsNodeCategory.Comment:\r\n        this.commentService.deleteComment([nodeData.id]);\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles move operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private handleMove(event: go.ChangedEvent, action: string): void {\r\n    if (event.object) {\r\n      const nodeData = event.object['changes']['iterator']\r\n        .filter(\r\n          (obj: go.ObjectData) =>\r\n            obj['propertyName'] == 'position' && !obj['object'].data\r\n        )\r\n        .first();\r\n      if (nodeData) {\r\n        if (\r\n          nodeData.object.category == GojsNodeCategory.Class ||\r\n          nodeData.object.category == GojsNodeCategory.AssociativeClass\r\n        ) {\r\n          this.classService.updateClass({\r\n            id: nodeData.object.id,\r\n            idDiagram: this.currentDiagram.id!,\r\n            idTemplateClass: nodeData.object.idTemplateClass,\r\n            key: nodeData.object.key,\r\n            name: nodeData.object.name,\r\n            description: nodeData.object.description,\r\n            volumetry: nodeData.object.volumetry,\r\n            tag: nodeData.object.tag,\r\n            isAssociative:\r\n              nodeData.object.category == GojsNodeCategory.AssociativeClass\r\n                ? true\r\n                : false,\r\n            property: {\r\n              color: nodeData.object.color,\r\n              height: nodeData.object.size.height,\r\n              width: nodeData.object.size.width,\r\n              icon: nodeData.object.icon,\r\n              position:\r\n                action === 'undo' ? nodeData.oldValue : nodeData.newValue,\r\n            },\r\n          });\r\n        } else if (nodeData.object.category == GojsNodeCategory.Enumeration) {\r\n          this.enumerationService.updateEnumeration({\r\n            id: nodeData.object.id,\r\n            idDiagram: this.currentDiagram.id!,\r\n            key: nodeData.object.key,\r\n            name: nodeData.object.name,\r\n            description: nodeData.object.description,\r\n            volumetry: nodeData.object.volumetry,\r\n            tag: nodeData.object.tag,\r\n            idTemplateEnumeration: nodeData.object.idTemplateEnumeration,\r\n            property: {\r\n              color: nodeData.object.color,\r\n              height: nodeData.object.size.height,\r\n              width: nodeData.object.size.width,\r\n              icon: nodeData.object.icon,\r\n              position:\r\n                action === 'undo' ? nodeData.oldValue : nodeData.newValue,\r\n            },\r\n          } as Enumeration);\r\n        } else if (nodeData.object.category == GojsNodeCategory.Comment) {\r\n          this.commentService.updateExistingComment({\r\n            id: nodeData.object.id,\r\n            name: nodeData.object.name,\r\n            description: nodeData.object.description,\r\n            height: nodeData.object.size.height,\r\n            width: nodeData.object.size.width,\r\n            position: action === 'undo' ? nodeData.oldValue : nodeData.newValue,\r\n          } as CommentPatch);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles resizing operations for the diagram.\r\n   * @private\r\n   * @param {go.ChangedEvent} event - The change event.\r\n   * @param {string} action - The action type ('undo' or 'redo').\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  private handleResizing(event: go.ChangedEvent, action: string): void {\r\n    if (event.object) {\r\n      const nodeData = event.object['changes']['iterator'].filter(\r\n        (obj: go.ObjectData) =>\r\n          obj['propertyName'] == 'desiredSize' && !obj['object'].data\r\n      )[0];\r\n      if (nodeData) {\r\n        this.classService.updateClass({\r\n          id: nodeData.object.id,\r\n          idDiagram: this.currentDiagram.id!,\r\n          idTemplateClass: nodeData.object.idTemplateClass,\r\n          key: nodeData.object.key,\r\n          name: nodeData.object.name,\r\n          isAssociative:\r\n            nodeData.object.category == GojsNodeCategory.AssociativeClass\r\n              ? true\r\n              : false,\r\n          property: {\r\n            color: nodeData.object.color,\r\n            height:\r\n              action === 'undo'\r\n                ? nodeData.oldValue.height\r\n                : nodeData.newValue.height,\r\n            width:\r\n              action === 'undo'\r\n                ? nodeData.oldValue.width\r\n                : nodeData.newValue.width,\r\n            icon: nodeData.object.icon,\r\n            position: nodeData.object.position,\r\n          },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  private handleLayoutChanges(event: go.ChangedEvent, action: string) {\r\n    if (event.object) {\r\n      let attributes: GojsDiagramAttributeNode[] = [];\r\n      event.object['changes']['iterator'].each((obj: go.ObjectData) => {\r\n        if (obj['propertyName'] == 'itemArray') {\r\n          attributes.push(...obj['oldValue']);\r\n        }\r\n      });\r\n      attributes = Array.from(\r\n        new Set(attributes.map((item) => JSON.stringify(item)))\r\n      ).map((item) => JSON.parse(item));\r\n\r\n      this.diagram.model.nodeDataArray.forEach((node) => {});\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,MAAM;AASrB,SAKEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAQ,8BAA8B;;;;;;;;;;;;;;;AAmBzD,OAAM,MAAOC,oBAAoB;EAI/BC,YACUC,aAA4B,EAC5BC,YAA0B,EAC1BC,sBAA8C,EAC9CC,eAAgC,EAChCC,YAA0B,EAC1BC,iBAAoC,EACpCC,sBAA8C,EAC9CC,kBAAsC,EACtCC,kBAAsC,EACtCC,eAAqC,EACrCC,gBAAkC,EAClCC,kBAAsC,EACtCC,cAA8B;IAZ9B,KAAAZ,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,cAAc,GAAdA,cAAc;IA0CxB;;;;;;IAMA,KAAAC,wBAAwB,GAAIC,KAAsB,IAAI;MACpD,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAMC,KAAK,GAAGF,KAAK,CAACG,OAAO;QAC3B,MAAMC,QAAQ,GAAa,EAAE;UAC3BC,OAAO,GAAa,EAAE;QAExB;QACA,IAAIC,QAAQ,GAAG,KAAK;QACpBJ,KAAK,CAACK,IAAI,CAAEC,IAAa,IAAI;UAC3B,IAAIA,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAAC6B,WAAW,EAAE;YACtDL,QAAQ,GAAG,IAAI;;QAEnB,CAAC,CAAC;QAEF;QACAN,KAAK,CAACY,MAAM,GAAG,IAAI;QAEnB;QACA,IAAIN,QAAQ,EAAE;UACZ,IAAI,CAACO,kBAAkB,CAACX,KAAK,EAAEF,KAAK,CAAC;SACtC,MAAM;UACL;UACA,IAAI,CAACc,qBAAqB,CAACZ,KAAK,EAAEE,QAAQ,EAAEC,OAAO,CAAC;;;IAG1D,CAAC;IAvEC,IAAI,CAACnB,aAAa,CAAC6B,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC5D,IAAIA,QAAQ,IAAIlC,UAAU,CAACmC,MAAM,EAAE;QACjC,IAAI,CAACjB,iBAAiB,GAAG,IAAI;OAC9B,MAAM;QACL,IAAI,CAACA,iBAAiB,GAAG,KAAK;;IAElC,CAAC,CAAC;IACF,IAAI,CAACX,YAAY,CAAC6B,oBAAoB,EAAE,CAACH,SAAS,CAAEI,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;IAEF,IAAI,CAAC7B,iBAAiB,CAAC+B,kBAAkB,EAAE,CAACN,SAAS,CAAEI,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACrC,CAAC,CAAC;EACJ;EAEA;;;;EAIAG,wBAAwBA,CAAA;IACtB,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEA;;;;EAIQL,4BAA4BA,CAAA;IAClC,IAAI,CAACJ,OAAO,CAACU,kBAAkB,CAC7B,mBAAmB,EACnB,IAAI,CAAC/B,wBAAwB,CAC9B;EACH;EAmCA;;;;;;EAMQc,kBAAkBA,CACxBX,KAAsB,EACtBF,KAAsB;IAEtBE,KAAK,CAACK,IAAI,CAAEC,IAAa,IAAI;MAC3B,IAAIA,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAAC6B,WAAW,EAAE;QACtD;QACA,IAAI,CAACvB,sBAAsB,CAAC2C,UAAU,CACpCvB,IAAI,EACJN,KAAK,CAAC8B,KAAK,EACX,IAAI,CAACZ,OAAO,EACZpB,KAAK,CACN;;IAEL,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOQc,qBAAqBA,CAC3BZ,KAAsB,EACtBE,QAAkB,EAClBC,OAAiB;IAEjBH,KAAK,CAACK,IAAI,CAAEC,IAAa,IAAI;MAC3B,IACEA,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACmD,KAAK,IAC5CzB,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACoD,gBAAgB,EACvD;QACA9B,QAAQ,CAAC+B,IAAI,CAAC3B,IAAI,CAACC,IAAI,CAAC2B,EAAE,CAAC;OAC5B,MAAM,IAAI5B,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACuD,WAAW,EAAE;QAC7DhC,OAAO,CAAC8B,IAAI,CAAC3B,IAAI,CAACC,IAAI,CAAC2B,EAAE,CAAC;OAC3B,MAAM,IAAI5B,IAAI,CAACC,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACwD,OAAO,EAAE;QACzD,IAAI,CAAC5C,kBAAkB,CAAC6C,mBAAmB,CAAC,CAAC/B,IAAI,CAACC,IAAI,CAAC2B,EAAE,CAAC,CAAC;;IAE/D,CAAC,CAAC;IAEF,IAAIhC,QAAQ,CAACoC,MAAM,GAAG,CAAC,EAAE,IAAI,CAACrD,YAAY,CAACsD,aAAa,CAACrC,QAAQ,CAAC;IAClE,IAAIC,OAAO,CAACmC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC/C,kBAAkB,CAACiD,kBAAkB,CAACrC,OAAO,CAAC;IAC3E,IAAI,CAAChB,eAAe,CAACsD,eAAe,CAAC,IAAI,CAAC;EAC5C;EAEA;;;;EAIQlB,sBAAsBA,CAAA;IAC5B,IAAI,CAACL,OAAO,CAACU,kBAAkB,CAAC,aAAa,EAAGc,CAAC,IAAI;MACnD,IAAI,IAAI,CAAC3C,iBAAiB,EAAE;QAC1B,IAAI,CAAC4C,iBAAiB,CAACD,CAAC,CAAC;;IAE7B,CAAC,CAAC;EACJ;EAEA;;;;;EAKQC,iBAAiBA,CAAC7C,KAAsB;IAC9C,MAAM8C,KAAK,GAAG9C,KAAK,CAACG,OAAmB;IACvC,IAAI4C,WAAW,GAAG/C,KAAK,CAACoB,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,EAAExC,IAAI;IACvDsC,WAAW,CAACG,IAAI,GAAG,IAAIrE,EAAE,CAACsE,IAAI,CAC5BL,KAAK,CAACM,YAAY,CAACC,KAAK,EACxBP,KAAK,CAACM,YAAY,CAACE,MAAM,CAC1B;IACD,IACEP,WAAW,CAACrC,QAAQ,KAAK5B,gBAAgB,CAACmD,KAAK,IAC/Cc,WAAW,CAACrC,QAAQ,KAAK5B,gBAAgB,CAACoD,gBAAgB,EAC1D;MACA,IAAI,CAACqB,WAAW,CAACR,WAAW,CAAC;KAC9B,MAAM,IAAIA,WAAW,CAACrC,QAAQ,KAAK5B,gBAAgB,CAACuD,WAAW,EAAE;MAChE,IAAI,CAAC7C,sBAAsB,CAACgE,kBAAkB,CAC5CT,WAAyC,EACzC,KAAK,CACN;KACF,MAAM,IAAIA,WAAW,CAACrC,QAAQ,KAAK5B,gBAAgB,CAACwD,OAAO,EAAE;MAC5D,IAAI,CAAC5C,kBAAkB,CAAC+D,aAAa,CACnCV,WAAW,CAACW,IAAI,EAChBX,WAAW,CAACY,WAAW,EACvBZ,WAAW,EACX/C,KAAK,CAACoB,OAAO,CACd;;EAEL;EAEA;;;;;EAKQmC,WAAWA,CAACR,WAAiC;IACnD,IAAI,CAAC5D,YAAY,CAACoE,WAAW,CAAC;MAC5BnB,EAAE,EAAEW,WAAW,CAACX,EAAE;MAClBsB,IAAI,EAAEX,WAAW,CAACW,IAAI;MACtBE,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;MAClCyB,GAAG,EAAEd,WAAW,CAACc,GAAG;MACpBC,aAAa,EAAEf,WAAW,CAACe,aAAa;MACxCC,QAAQ,EAAE;QACRC,QAAQ,EAAEjB,WAAW,CAACiB,QAAS;QAC/BV,MAAM,EAAEP,WAAW,CAACG,IAAI,CAACI,MAAM;QAC/BD,KAAK,EAAEN,WAAW,CAACG,IAAI,CAACG,KAAK;QAC7BY,IAAI,EAAElB,WAAW,CAACkB,IAAI;QACtBC,KAAK,EAAEnB,WAAW,CAACmB;;KAEtB,CAAC;EACJ;EAEA;;;;EAIQxC,qBAAqBA,CAAA;IAC3B,IAAI,CAACN,OAAO,CAACU,kBAAkB,CAAC,YAAY,EAAGc,CAAC,IAAI;MAClD,IACEA,CAAC,CAACzC,OAAO,CAACgE,KAAK,KACdvB,CAAC,CAACzC,OAAO,CAACgE,KAAK,CAAC1D,IAAI,IACnBmC,CAAC,CAACxB,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,EAAExC,IAAI,CAACC,QAAQ,KACxC5B,gBAAgB,CAAC6B,WAAW,CAAC,IACjC,IAAI,CAACV,iBAAiB,EACtB;QACA,IAAI,CAACmE,wBAAwB,CAACxB,CAAC,CAAC;;IAEpC,CAAC,CAAC;EACJ;EAEA;;;;;;EAMQwB,wBAAwBA,CAACpE,KAAsB;IACrD,IACEA,KAAK,CAACoB,OAAO,CAAC4B,SAAS,IACvBhD,KAAK,CAACoB,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,EAAExC,IAAI,IACrCT,KAAK,CAACoB,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,EAAExC,IAAI,CAACC,QAAQ,IAC5C5B,gBAAgB,CAAC6B,WAAW,EAC9B;MACA,IAAI,CAACvB,sBAAsB,CAACiF,sBAAsB,CAChDrE,KAAK,CAACoB,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,EAAExC,IAAI,CACtC;KACF,MAAM,IACLT,KAAK,CAACG,OAAO,CAACgE,KAAK,CAAC1D,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACwF,SAAS,IAC/DtE,KAAK,CAACG,OAAO,CAACgE,KAAK,CAAC1D,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAACyF,SAAS,EAC/D;MACA,IAAI,CAAC5E,eAAe,CAAC6E,2BAA2B,CAACxE,KAAK,EAAE,IAAI,CAACoB,OAAO,CAAC;KACtE,MAAM,IACLpB,KAAK,CAACG,OAAO,CAACgE,KAAK,CAAC1D,IAAI,CAACC,QAAQ,IAAI5B,gBAAgB,CAAC2F,kBAAkB,EACxE;MACA,IAAI,CAAC5E,kBAAkB,CAAC6E,8BAA8B,CACpD1E,KAAK,EACL,IAAI,CAACoB,OAAO,CACb;;EAEL;EAEA;;;;EAIQO,oBAAoBA,CAAA;IAC1B,IAAI,CAACP,OAAO,CAACU,kBAAkB,CAAC,WAAW,EAAG9B,KAAK,IAAI;MACrD,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,IAAI,CAAC0E,eAAe,CAAC3E,KAAK,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEA;;;;;;EAMQ2E,eAAeA,CAAC3E,KAAsB;IAC5C,MAAM4E,QAAQ,GAAsC5E,KAAK,CAACG,OAAO,CAACM,IAAI;IACtE,MAAMoE,MAAM,GAAG,IAAI,CAACvF,YAAY,CAACwF,kBAAkB,CACjD,IAAI,CAAC1D,OAAO,EACZwD,QAAQ,CAACG,EAAE,CACZ;IACD,MAAMC,QAAQ,GAAG,IAAI,CAAC1F,YAAY,CAACwF,kBAAkB,CACnD,IAAI,CAAC1D,OAAO,EACZwD,QAAQ,CAACK,IAAI,CACd;IACD,IAAIJ,MAAM,IAAIG,QAAQ,EAAE;MACtB,IACEA,QAAQ,CAAC,UAAU,CAAC,IAAIlG,gBAAgB,CAACoD,gBAAgB,IACzD2C,MAAM,CAAC,UAAU,CAAC,IAAI/F,gBAAgB,CAACoG,SAAS,EAChD;QACA,MAAMC,aAAa,GAAG,IAAI,CAACC,sBAAsB,CAC/CJ,QAAQ,EACRH,MAAM,EACND,QAA8B,CAC/B;QACD,IAAI,CAACxF,sBAAsB,CAACiG,4BAA4B,CACtDT,QAA8B,EAC9BO,aAAa,EACb,IAAI,CAAC/D,OAAO,CACb;OACF,MAAM;QACL,MAAMkE,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,QAAQ,EAAEH,MAAM,CAAC;QAC1D,MAAMW,OAAO,GAAG,IAAI,CAACC,gBAAgB,CACnCT,QAAQ,EACRH,MAAM,EACND,QAAwB,CACzB;QACD,IAAIU,WAAW,EAAE;UACf,IAAI,CAAClG,sBAAsB,CAACsG,iBAAiB,CAC3CJ,WAAW,EACXV,QAAwB,EACxBY,OAAO,EACPR,QAAQ,EACRH,MAAM,EACN,IAAI,CAACzD,OAAO,CACb;SACF,MAAM;UACL,IAAI,CAAChC,sBAAsB,CAACuG,sBAAsB,CAChDf,QAAwB,EACxBY,OAAO,EACPR,QAAQ,EACRH,MAAM,EACN,IAAI,CAACzD,OAAO,CACb;UACDpB,KAAK,CAACoB,OAAO,CAACwE,cAAc,EAAE;;;;EAItC;EAEA;;;;;;;EAOQL,eAAeA,CACrBP,QAAuB,EACvBH,MAAqB;IAErB,OAAO,IAAI,CAACvF,YAAY,CACrBuG,eAAe,EAAE,CACjBC,IAAI,CACFC,IAAI,IACFA,IAAI,CAACC,aAAa,IAAIhB,QAAQ,CAAC,IAAI,CAAC,IACnCe,IAAI,CAACE,kBAAkB,IAAIpB,MAAM,CAAC,IAAI,CAAC,IACxCkB,IAAI,CAACC,aAAa,IAAInB,MAAM,CAAC,IAAI,CAAC,IACjCkB,IAAI,CAACE,kBAAkB,IAAIjB,QAAQ,CAAC,IAAI,CAAE,CAC/C;EACL;EAEA;;;;;;;;EAQQS,gBAAgBA,CACtBT,QAAuB,EACvBH,MAAqB,EACrBD,QAAsB;IAEtB,OAAO;MACLlB,IAAI,EAAE,GAAGsB,QAAQ,CAAC,MAAM,CAAC,MAAMH,MAAM,CAAC,MAAM,CAAC,EAAE;MAC/CzC,EAAE,EAAEwC,QAAQ,CAACxC,EAAE;MACf8D,iBAAiB,EAAElB,QAAQ,CAAC,iBAAiB,CAAC;MAC9CmB,sBAAsB,EAAEtB,MAAM,CAAC,iBAAiB,CAAC;MACjDuB,UAAU,EAAE,CAAC;MACbC,UAAU,EAAEzB,QAAQ,CAAC0B,QAAS;MAC9BC,eAAe,EAAE3B,QAAQ,CAAC4B,MAAO;MACjC5C,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;MAClC8B,KAAK,EAAE,kBAAkB;MACzBuC,WAAW,EAAE7B,QAAQ,CAAC6B,WAAW;MACjCC,SAAS,EAAE9B,QAAQ,CAAC8B,SAAS;MAC7BC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE;QACRhD,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;QAClCmE,eAAe,EAAE3B,QAAQ,CAAC4B,MAAO;QACjCH,UAAU,EAAEzB,QAAQ,CAAC0B,QAAS;QAC9BO,MAAM,EAAEjC,QAAQ,CAACxC,EAAG;QACpBuE,aAAa,EAAE;;KAElB;EACH;EACQvB,sBAAsBA,CAC5BJ,QAAuB,EACvBH,MAAqB,EACrBD,QAA4B;IAE5B,OAAO;MACLiC,MAAM,EAAEhC,MAAM,CAAC,QAAQ,CAAC;MACxBiC,kBAAkB,EAAE9B,QAAQ,CAAC,iBAAiB,CAAC;MAC/C+B,IAAI,EAAEnC,QAAQ,CAAC0B;KAChB;EACH;EAEQ1E,sBAAsBA,CAAA;IAC5B,IAAI,CAACR,OAAO,CAACU,kBAAkB,CAAC,cAAc,EAAG9B,KAAK,IAAI;MACxD,IAAI,IAAI,CAACC,iBAAiB,EAAE;QAC1B,MAAM2E,QAAQ,GAAG5E,KAAK,CAACG,OAAO,CAACM,IAAI;QACnC,IAAImE,QAAQ,EAAE;UACZ,IAAIA,QAAQ,CAAClE,QAAQ,IAAI5B,gBAAgB,CAACkI,UAAU,EAAE;YACpD,IAAI,CAAC5H,sBAAsB,CAAC6H,uBAAuB,CAACrC,QAAQ,CAAC;WAC9D,MAAM;YACL,IAAI,CAACxF,sBAAsB,CAAC8H,iBAAiB,CAC3CtC,QAAQ,EACR,IAAI,CAACxD,OAAO,CACb;YACDpB,KAAK,CAACoB,OAAO,CAACwE,cAAc,EAAE;;;;IAItC,CAAC,CAAC;EACJ;EAEA;;;;EAIQ/D,uBAAuBA,CAAA;IAC7B,IAAI,CAACT,OAAO,CAACS,uBAAuB,CAAE7B,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACmH,qBAAqB,EAAE;QAC/B,MAAMC,MAAM,GACVpH,KAAK,CAACqH,YAAY,KAAK,cAAc,GACjC,MAAM,GACNrH,KAAK,CAACqH,YAAY,KAAK,cAAc,GACrC,MAAM,GACN,IAAI;QACV,IAAID,MAAM,EAAE;UACV,IAAI,CAACE,cAAc,CAACtH,KAAK,EAAEoH,MAAM,CAAC;;QAGpC,MAAMG,cAAc,GAClBvH,KAAK,CAACqH,YAAY,KAAK,sBAAsB,IAC7CrH,KAAK,CAACwH,QAAQ,KAAK,eAAe;QACpC,IAAID,cAAc,EAAE;UAClB,IAAIvH,KAAK,CAACyH,MAAM,EAAE;YAChB,IAAId,aAAa;YACjB,IAAI/B,QAAQ;YACZ5E,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAClH,IAAI,CACrCmH,MAAqB,IAAI;cACxB,IAAIA,MAAM,CAAC,cAAc,CAAC,KAAK,eAAe,EAAE;gBAC9Cf,aAAa,GAAGe,MAAM,CAAC,UAAU,CAAC;gBAClC9C,QAAQ,GAAG8C,MAAM,CAAC,QAAQ,CAAC;;YAE/B,CAAC,CACF;YACD,IAAI,CAACtI,sBAAsB,CAACiF,sBAAsB,CAChDO,QAAyB,CAC1B;;;;IAIT,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOQ0C,cAAcA,CAACtH,KAAsB,EAAEoH,MAAc;IAC3D,IAAIpH,KAAK,CAACyH,MAAM,EAAE;MAChB,IAAIzH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,QAAQ,EAAE;QACpC,IAAI,CAACE,YAAY,CAAC3H,KAAK,EAAEoH,MAAM,CAAC;OACjC,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,cAAc,EAAE;QACjD,IAAI,CAACG,kBAAkB,CAAC5H,KAAK,EAAEoH,MAAM,CAAC;OACvC,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,SAAS,EAAE;QAC5C,IAAI,CAACrI,sBAAsB,CAACyI,qBAAqB,CAAC7H,KAAK,EAAEoH,MAAM,EAAE,KAAK,CAAC;OACxE,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,EAAE;QACzC,IAAI,CAACK,UAAU,CAAC9H,KAAK,EAAEoH,MAAM,CAAC;OAC/B,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE;QAC7C,IAAI,CAACM,cAAc,CAAC/H,KAAK,EAAEoH,MAAM,CAAC;OACnC,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,aAAa,EAAE;QAChD,IAAI,CAACO,yBAAyB,CAAChI,KAAK,EAAEoH,MAAM,CAAC;OAC9C,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,QAAQ,EAAE;QAC3C,IAAI,CAACQ,mBAAmB,CAACjI,KAAK,EAAEoH,MAAM,CAAC;OACxC,MAAM,IAAIpH,KAAK,CAACyH,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE;QACrC,IAAI,CAACE,YAAY,CAAC3H,KAAK,EAAEoH,MAAM,CAAC;;;EAGtC;EAEQY,yBAAyBA,CAAChI,KAAsB,EAAEoH,MAAc;IACtE,IAAI,CAACpH,KAAK,CAACyH,MAAM,EAAE;IAEnB,MAAMS,eAAe,GAAGlI,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC;IAE3DS,eAAe,CAAC3H,IAAI,CAAEmH,MAAqB,IAAI;MAC7C,IAAIA,MAAM,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE;QACrC,IAAI,CAACS,gBAAgB,CAACT,MAAM,EAAEN,MAAM,CAAC;OACtC,MAAM,IAAIM,MAAM,CAAC,cAAc,CAAC,KAAK,aAAa,EAAE;QACnD,IAAI,CAACU,uBAAuB,CAACV,MAAM,EAAEN,MAAM,CAAC;;IAEhD,CAAC,CAAC;EACJ;EAEQe,gBAAgBA,CAACT,MAAqB,EAAEN,MAAc;IAC5D,MAAMiB,YAAY,GAChBjB,MAAM,KAAK,MAAM,GAAGM,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC;IAE7D,MAAMY,SAAS,GAAG,IAAI,CAACC,eAAe,CAACb,MAAM,CAAC;IAC9C,IAAI,CAACY,SAAS,EAAE;IAEhB,QAAQA,SAAS,CAAC7H,IAAI,CAACC,QAAQ;MAC7B,KAAK5B,gBAAgB,CAACwD,OAAO;QAC3B,IAAI,CAAC5C,kBAAkB,CAAC+D,aAAa,CACnC4E,YAAY,EACZC,SAAS,CAAC7H,IAAI,CAACkD,WAAW,EAC1B2E,SAAS,CAAC7H,IAAI,EACd,IAAI,CAACW,OAAO,CACb;QACD;MACF,KAAKtC,gBAAgB,CAAC6B,WAAW;QAC/B,IAAI,CAACvB,sBAAsB,CAACiF,sBAAsB,CAAC;UACjD,GAAGiE,SAAS,CAAC7H;SACd,CAAC;QACF;MACF,KAAK3B,gBAAgB,CAACmD,KAAK;MAC3B,KAAKnD,gBAAgB,CAACoD,gBAAgB;QACpC,IAAI,CAACtC,gBAAgB,CAAC4I,mBAAmB,CAACF,SAAS,CAAC7H,IAAI,EAAE,IAAI,CAACW,OAAO,CAAC;QACvE;MACF,KAAKtC,gBAAgB,CAACuD,WAAW;QAC/B,IAAI,CAAC7C,sBAAsB,CAACiJ,4BAA4B,CACtDH,SAAS,CAAC7H,IAAI,EACd,IAAI,CAACW,OAAO,CACb;QACD;;EAEN;EAEQgH,uBAAuBA,CAACV,MAAqB,EAAEN,MAAc;IACnE,MAAMsB,mBAAmB,GACvBtB,MAAM,KAAK,MAAM,GAAGM,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC;IAE7D,MAAMY,SAAS,GAAG,IAAI,CAACC,eAAe,CAACb,MAAM,CAAC;IAC9C,IAAIY,SAAS,EAAE7H,IAAI,CAACC,QAAQ,KAAK5B,gBAAgB,CAACwD,OAAO,EAAE;MACzD,IAAI,CAAC5C,kBAAkB,CAAC+D,aAAa,CACnC6E,SAAS,CAAC7H,IAAI,CAACiD,IAAI,EACnBgF,mBAAmB,EACnBJ,SAAS,CAAC7H,IAAI,EACd,IAAI,CAACW,OAAO,CACb;;EAEL;EAEQmH,eAAeA,CAACb,MAAqB;IAC3C;IACA,MAAMiB,iBAAiB,GAAGjB,MAAM,CAAC,SAAS,CAAC;IAC3C,IAAIiB,iBAAiB,EAAE3F,SAAS,EAAEC,KAAK,EAAE;MACvC,OAAO0F,iBAAiB,CAAC3F,SAAS,CAACC,KAAK,EAAE,IAAI,IAAI;;IAGpD;IACA,IAAI,IAAI,CAAC7B,OAAO,EAAE4B,SAAS,EAAEC,KAAK,EAAE;MAClC,OAAO,IAAI,CAAC7B,OAAO,CAAC4B,SAAS,CAACC,KAAK,EAAE,IAAI,IAAI;;IAG/C;IACA,IAAIyE,MAAM,CAAC,QAAQ,CAAC,IAAIA,MAAM,CAAC,QAAQ,CAAC,CAAC7D,GAAG,IAAI,IAAI,CAACzC,OAAO,EAAE;MAC5D,MAAMyC,GAAG,GAAG6D,MAAM,CAAC,QAAQ,CAAC,CAAC7D,GAAG;MAChC,OAAO,IAAI,CAACzC,OAAO,CAACwH,cAAc,CAAC/E,GAAG,CAAC,IAAI,IAAI;;IAGjD;IACA,IAAI6D,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACtG,OAAO,EAAE;MACpC,OAAO,IAAI,CAAC,CAAC;;IAGf,OAAO,IAAI;EACb;EAEA;;;;;;;EAOQuG,YAAYA,CAAC3H,KAAsB,EAAEoH,MAAc;IACzD,IAAI,CAACpH,KAAK,CAACyH,MAAM,EAAE;IACnB,MAAMS,eAAe,GAAGlI,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC;IAC3DS,eAAe,CAAC3H,IAAI,CAAEmH,MAAqB,IAAI;MAC7C,IAAIA,MAAM,CAAC,cAAc,CAAC,KAAK,eAAe,EAAE;QAC9C,IAAI,CAACmB,kBAAkB,CAACnB,MAAM,CAAC,UAAU,CAAC,EAAEN,MAAM,CAAC;OACpD,MAAM,IAAIM,MAAM,CAAC,cAAc,CAAC,KAAK,eAAe,EAAE;QACrD,IAAI,CAAC7G,kBAAkB,CAACb,KAAK,EAAEoH,MAAM,CAAC;;IAE1C,CAAC,CAAC;EACJ;EAEQyB,kBAAkBA,CAACC,QAAa,EAAE1B,MAAc;IACtD,IAAI,CAAC0B,QAAQ,EAAE;IACf,MAAMC,MAAM,GAAGD,QAAQ,CAAC1G,EAAE;IAC1B,MAAM1B,QAAQ,GAAGoI,QAAQ,CAACpI,QAAQ;IAClC,IAAI0G,MAAM,KAAK,MAAM,EAAE;MACrB,IAAI,CAAC4B,gBAAgB,CAACtI,QAAQ,EAAEqI,MAAM,CAAC;KACxC,MAAM;MACL,IAAI,CAACE,mBAAmB,CAACvI,QAAQ,EAAEqI,MAAM,CAAC;;EAE9C;EAEQC,gBAAgBA,CAACtI,QAA0B,EAAEqI,MAAc;IACjE,QAAQrI,QAAQ;MACd,KAAK5B,gBAAgB,CAACmD,KAAK;MAC3B,KAAKnD,gBAAgB,CAACoD,gBAAgB;QACpC,IAAI,CAAC/C,YAAY,CAAC+J,iBAAiB,CAACH,MAAM,CAAC;QAC3C;MACF,KAAKjK,gBAAgB,CAACuD,WAAW;QAC/B,IAAI,CAAC5C,kBAAkB,CAAC0J,gBAAgB,CAACJ,MAAM,CAAC;QAChD;MACF,KAAKjK,gBAAgB,CAACwD,OAAO;QAC3B,IAAI,CAACxC,cAAc,CAACsJ,mBAAmB,CAACL,MAAM,CAAC;QAC/C;;EAEN;EAEQE,mBAAmBA,CACzBvI,QAA0B,EAC1BqI,MAAc;IAEd,QAAQrI,QAAQ;MACd,KAAK5B,gBAAgB,CAACmD,KAAK;MAC3B,KAAKnD,gBAAgB,CAACoD,gBAAgB;QACpC,IAAI,CAAC/C,YAAY,CAACsD,aAAa,CAAC,CAACsG,MAAM,CAAC,CAAC;QACzC;MACF,KAAKjK,gBAAgB,CAACuD,WAAW;QAC/B,IAAI,CAAC5C,kBAAkB,CAACiD,kBAAkB,CAAC,CAACqG,MAAM,CAAC,CAAC;QACpD;MACF,KAAKjK,gBAAgB,CAACwD,OAAO;QAC3B,IAAI,CAACxC,cAAc,CAACuJ,aAAa,CAAC,CAACN,MAAM,CAAC,CAAC;QAC3C;;EAEN;EAEQlI,kBAAkBA,CAACb,KAAsB,EAAEoH,MAAc;IAC/D,IAAI,CAAChI,sBAAsB,CAACyI,qBAAqB,CAC/C7H,KAAK,EACLoH,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,EACnC,IAAI,CACL;EACH;EAEA;;;;;;;EAOQQ,kBAAkBA,CAAC5H,KAAsB,EAAEoH,MAAc;IAC/D,IAAI,CAACpH,KAAK,CAACyH,MAAM,EAAE;IACnB,MAAMS,eAAe,GAAGlI,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC;IAC3DS,eAAe,CAAC3H,IAAI,CAAEmH,MAAqB,IAAI;MAC7C,IAAIA,MAAM,CAAC,cAAc,CAAC,KAAK,eAAe,EAAE;QAC9C,MAAMoB,QAAQ,GAAGpB,MAAM,CAAC,UAAU,CAAC;QACnC,IAAI,CAACoB,QAAQ,EAAE;QAEf,IAAI1B,MAAM,KAAK,MAAM,EAAE;UACrB,IAAI,CAACkC,eAAe,CAACR,QAAQ,CAAC;SAC/B,MAAM;UACL,IAAI,CAACS,cAAc,CAACT,QAAQ,CAAC;UAC7B,IAAI,CAAC1H,OAAO,CAACoI,KAAK,CAACC,oBAAoB,CAACX,QAAQ,CAAC;;;IAGvD,CAAC,CAAC;EACJ;EAEQQ,eAAeA,CAACR,QAAa;IACnC,QAAQA,QAAQ,CAACpI,QAAQ;MACvB,KAAK5B,gBAAgB,CAACmD,KAAK;MAC3B,KAAKnD,gBAAgB,CAACoD,gBAAgB;QACpC,IAAI,CAAC/C,YAAY,CAAC+J,iBAAiB,CAACJ,QAAQ,CAAC1G,EAAE,CAAC;QAChD;MACF,KAAKtD,gBAAgB,CAACuD,WAAW;QAC/B,IAAI,CAAC5C,kBAAkB,CAAC0J,gBAAgB,CAACL,QAAQ,CAAC1G,EAAE,CAAC;QACrD;MACF,KAAKtD,gBAAgB,CAACwD,OAAO;QAC3B,IAAI,CAACxC,cAAc,CAACsJ,mBAAmB,CAACN,QAAQ,CAAC1G,EAAE,CAAC;QACpD;;EAEN;EAEQmH,cAAcA,CAACT,QAAa;IAClC,QAAQA,QAAQ,CAACpI,QAAQ;MACvB,KAAK5B,gBAAgB,CAACmD,KAAK;MAC3B,KAAKnD,gBAAgB,CAACoD,gBAAgB;QACpC,IAAI,CAAC/C,YAAY,CAACsD,aAAa,CAAC,CAACqG,QAAQ,CAAC1G,EAAE,CAAC,CAAC;QAC9C;MACF,KAAKtD,gBAAgB,CAACuD,WAAW;QAC/B,IAAI,CAAC5C,kBAAkB,CAACiK,mBAAmB,CAAC,CAACZ,QAAQ,CAAC1G,EAAE,CAAC,CAAC;QAC1D;MACF,KAAKtD,gBAAgB,CAACwD,OAAO;QAC3B,IAAI,CAACxC,cAAc,CAACuJ,aAAa,CAAC,CAACP,QAAQ,CAAC1G,EAAE,CAAC,CAAC;QAChD;;EAEN;EAEA;;;;;;;EAOQ0F,UAAUA,CAAC9H,KAAsB,EAAEoH,MAAc;IACvD,IAAIpH,KAAK,CAACyH,MAAM,EAAE;MAChB,MAAMqB,QAAQ,GAAG9I,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CACjDkC,MAAM,CACJC,GAAkB,IACjBA,GAAG,CAAC,cAAc,CAAC,IAAI,UAAU,IAAI,CAACA,GAAG,CAAC,QAAQ,CAAC,CAACnJ,IAAI,CAC3D,CACAwC,KAAK,EAAE;MACV,IAAI6F,QAAQ,EAAE;QACZ,IACEA,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACmD,KAAK,IAClD6G,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACoD,gBAAgB,EAC7D;UACA,IAAI,CAAC/C,YAAY,CAACoE,WAAW,CAAC;YAC5BnB,EAAE,EAAE0G,QAAQ,CAACrB,MAAM,CAACrF,EAAE;YACtBwB,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;YAClCyH,eAAe,EAAEf,QAAQ,CAACrB,MAAM,CAACoC,eAAe;YAChDhG,GAAG,EAAEiF,QAAQ,CAACrB,MAAM,CAAC5D,GAAG;YACxBH,IAAI,EAAEoF,QAAQ,CAACrB,MAAM,CAAC/D,IAAI;YAC1BC,WAAW,EAAEmF,QAAQ,CAACrB,MAAM,CAAC9D,WAAW;YACxCmG,SAAS,EAAEhB,QAAQ,CAACrB,MAAM,CAACqC,SAAS;YACpCC,GAAG,EAAEjB,QAAQ,CAACrB,MAAM,CAACsC,GAAG;YACxBjG,aAAa,EACXgF,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACoD,gBAAgB,GACzD,IAAI,GACJ,KAAK;YACX6B,QAAQ,EAAE;cACRG,KAAK,EAAE4E,QAAQ,CAACrB,MAAM,CAACvD,KAAK;cAC5BZ,MAAM,EAAEwF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACI,MAAM;cACnCD,KAAK,EAAEyF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACG,KAAK;cACjCY,IAAI,EAAE6E,QAAQ,CAACrB,MAAM,CAACxD,IAAI;cAC1BD,QAAQ,EACNoD,MAAM,KAAK,MAAM,GAAG0B,QAAQ,CAACtB,QAAQ,GAAGsB,QAAQ,CAACkB;;WAEtD,CAAC;SACH,MAAM,IAAIlB,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACuD,WAAW,EAAE;UACnE,IAAI,CAAC5C,kBAAkB,CAACwK,iBAAiB,CAAC;YACxC7H,EAAE,EAAE0G,QAAQ,CAACrB,MAAM,CAACrF,EAAE;YACtBwB,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;YAClCyB,GAAG,EAAEiF,QAAQ,CAACrB,MAAM,CAAC5D,GAAG;YACxBH,IAAI,EAAEoF,QAAQ,CAACrB,MAAM,CAAC/D,IAAI;YAC1BC,WAAW,EAAEmF,QAAQ,CAACrB,MAAM,CAAC9D,WAAW;YACxCmG,SAAS,EAAEhB,QAAQ,CAACrB,MAAM,CAACqC,SAAS;YACpCC,GAAG,EAAEjB,QAAQ,CAACrB,MAAM,CAACsC,GAAG;YACxBG,qBAAqB,EAAEpB,QAAQ,CAACrB,MAAM,CAACyC,qBAAqB;YAC5DnG,QAAQ,EAAE;cACRG,KAAK,EAAE4E,QAAQ,CAACrB,MAAM,CAACvD,KAAK;cAC5BZ,MAAM,EAAEwF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACI,MAAM;cACnCD,KAAK,EAAEyF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACG,KAAK;cACjCY,IAAI,EAAE6E,QAAQ,CAACrB,MAAM,CAACxD,IAAI;cAC1BD,QAAQ,EACNoD,MAAM,KAAK,MAAM,GAAG0B,QAAQ,CAACtB,QAAQ,GAAGsB,QAAQ,CAACkB;;WAEvC,CAAC;SAClB,MAAM,IAAIlB,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACwD,OAAO,EAAE;UAC/D,IAAI,CAACxC,cAAc,CAACqK,qBAAqB,CAAC;YACxC/H,EAAE,EAAE0G,QAAQ,CAACrB,MAAM,CAACrF,EAAE;YACtBsB,IAAI,EAAEoF,QAAQ,CAACrB,MAAM,CAAC/D,IAAI;YAC1BC,WAAW,EAAEmF,QAAQ,CAACrB,MAAM,CAAC9D,WAAW;YACxCL,MAAM,EAAEwF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACI,MAAM;YACnCD,KAAK,EAAEyF,QAAQ,CAACrB,MAAM,CAACvE,IAAI,CAACG,KAAK;YACjCW,QAAQ,EAAEoD,MAAM,KAAK,MAAM,GAAG0B,QAAQ,CAACtB,QAAQ,GAAGsB,QAAQ,CAACkB;WAC5C,CAAC;;;;EAI1B;EAEA;;;;;;;EAOQjC,cAAcA,CAAC/H,KAAsB,EAAEoH,MAAc;IAC3D,IAAIpH,KAAK,CAACyH,MAAM,EAAE;MAChB,MAAMqB,QAAQ,GAAG9I,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAACkC,MAAM,CACxDC,GAAkB,IACjBA,GAAG,CAAC,cAAc,CAAC,IAAI,aAAa,IAAI,CAACA,GAAG,CAAC,QAAQ,CAAC,CAACnJ,IAAI,CAC9D,CAAC,CAAC,CAAC;MACJ,IAAIqI,QAAQ,EAAE;QACZ,IAAI,CAAC3J,YAAY,CAACoE,WAAW,CAAC;UAC5BnB,EAAE,EAAE0G,QAAQ,CAACrB,MAAM,CAACrF,EAAE;UACtBwB,SAAS,EAAE,IAAI,CAACvC,cAAc,CAACe,EAAG;UAClCyH,eAAe,EAAEf,QAAQ,CAACrB,MAAM,CAACoC,eAAe;UAChDhG,GAAG,EAAEiF,QAAQ,CAACrB,MAAM,CAAC5D,GAAG;UACxBH,IAAI,EAAEoF,QAAQ,CAACrB,MAAM,CAAC/D,IAAI;UAC1BI,aAAa,EACXgF,QAAQ,CAACrB,MAAM,CAAC/G,QAAQ,IAAI5B,gBAAgB,CAACoD,gBAAgB,GACzD,IAAI,GACJ,KAAK;UACX6B,QAAQ,EAAE;YACRG,KAAK,EAAE4E,QAAQ,CAACrB,MAAM,CAACvD,KAAK;YAC5BZ,MAAM,EACJ8D,MAAM,KAAK,MAAM,GACb0B,QAAQ,CAACtB,QAAQ,CAAClE,MAAM,GACxBwF,QAAQ,CAACkB,QAAQ,CAAC1G,MAAM;YAC9BD,KAAK,EACH+D,MAAM,KAAK,MAAM,GACb0B,QAAQ,CAACtB,QAAQ,CAACnE,KAAK,GACvByF,QAAQ,CAACkB,QAAQ,CAAC3G,KAAK;YAC7BY,IAAI,EAAE6E,QAAQ,CAACrB,MAAM,CAACxD,IAAI;YAC1BD,QAAQ,EAAE8E,QAAQ,CAACrB,MAAM,CAACzD;;SAE7B,CAAC;;;EAGR;EAEQiE,mBAAmBA,CAACjI,KAAsB,EAAEoH,MAAc;IAChE,IAAIpH,KAAK,CAACyH,MAAM,EAAE;MAChB,IAAI2C,UAAU,GAA+B,EAAE;MAC/CpK,KAAK,CAACyH,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAClH,IAAI,CAAEqJ,GAAkB,IAAI;QAC9D,IAAIA,GAAG,CAAC,cAAc,CAAC,IAAI,WAAW,EAAE;UACtCQ,UAAU,CAACjI,IAAI,CAAC,GAAGyH,GAAG,CAAC,UAAU,CAAC,CAAC;;MAEvC,CAAC,CAAC;MACFQ,UAAU,GAAGC,KAAK,CAACpF,IAAI,CACrB,IAAIqF,GAAG,CAACF,UAAU,CAACG,GAAG,CAAEC,IAAI,IAAKC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC,CAAC,CACxD,CAACD,GAAG,CAAEC,IAAI,IAAKC,IAAI,CAACE,KAAK,CAACH,IAAI,CAAC,CAAC;MAEjC,IAAI,CAACpJ,OAAO,CAACoI,KAAK,CAACoB,aAAa,CAACC,OAAO,CAAErK,IAAI,IAAI,CAAE,CAAC,CAAC;;EAE1D;EAAC,QAAAsK,CAAA,G;qBAr0BU9L,oBAAoB,EAAA+L,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,sBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,iBAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,sBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,kBAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,kBAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,oBAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,gBAAA,GAAAvB,EAAA,CAAAC,QAAA,CAAAuB,GAAA,CAAAC,kBAAA,GAAAzB,EAAA,CAAAC,QAAA,CAAAyB,GAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;WAApB3N,oBAAoB;IAAA4N,OAAA,EAApB5N,oBAAoB,CAAA6N,IAAA;IAAAC,UAAA,EAFnB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}